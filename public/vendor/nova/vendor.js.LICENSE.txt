/* NProgress, (c) 2013, 2014 Rico Sta<PERSON> Cruz - http://ricostacruz.com/nprogress
 * @license MIT */

/*!
			 * numbro.js language configuration
			 * language : Bulgarian
			 * author : <PERSON> (StayinFront NZ)
			 */

/*!
			 * numbro.js language configuration
			 * language : Chinese (Taiwan)
			 * author (numbro.js Version): <PERSON> : https://github.com/rocketedaway
			 * author (numeral.js Version) : <PERSON> : https://github.com/pedantic-git
			 */

/*!
			 * numbro.js language configuration
			 * language : Chinese simplified
			 * locale: Singapore
			 * author : <PERSON> (StayinFront NZ)
			 */

/*!
			 * numbro.js language configuration
			 * language : Chinese traditional
			 * locale: Macau
			 * author : <PERSON> (StayinFront NZ)
			 */

/*!
			 * numbro.js language configuration
			 * language : Czech
			 * locale: Czech Republic
			 * author : <PERSON> : https://github.com/smajl (based on work from <PERSON><PERSON><PERSON> : https://github.com/apapirovski)
			 */

/*!
			 * numbro.js language configuration
			 * language : Danish
			 * locale: Denmark
			 * author : <PERSON> : https://github.com/msto<PERSON>ard
			 */

/*!
			 * numbro.js language configuration
			 * language : Dutch
			 * locale: Belgium
			 * author : <PERSON><PERSON>paert : https://github.com/moeriki
			 */

/*!
			 * numbro.js language configuration
			 * language : Dutch
			 * locale: Netherlands
			 * author : Dave Clayton : https://github.com/davedx
			 */

/*!
			 * numbro.js language configuration
			 * language : English
			 * locale: Australia
			 * author : Benedikt Huss : https://github.com/ben305
			 */

/*!
			 * numbro.js language configuration
			 * language : English
			 * locale: New Zealand
			 * author : Benedikt Huss : https://github.com/ben305
			 */

/*!
			 * numbro.js language configuration
			 * language : English
			 * locale: South Africa
			 * author : Stewart Scott https://github.com/stewart42
			 */

/*!
			 * numbro.js language configuration
			 * language : English
			 * locale: United Kingdom of Great Britain and Northern Ireland
			 * author : Dan Ristic : https://github.com/dristic
			 */

/*!
			 * numbro.js language configuration
			 * language : Estonian
			 * locale: Estonia
			 * author : Illimar Tambek : https://github.com/ragulka
			 *
			 * Note: in Estonian, abbreviations are always separated
			 * from numbers with a space
			 */

/*!
			 * numbro.js language configuration
			 * language : Farsi
			 * locale: Iran
			 * author : neo13 : https://github.com/neo13
			 */

/*!
			 * numbro.js language configuration
			 * language : Filipino (Pilipino)
			 * locale: Philippines
			 * author : Michael Abadilla : https://github.com/mjmaix
			 */

/*!
			 * numbro.js language configuration
			 * language : Finnish
			 * locale: Finland
			 * author : Sami Saada : https://github.com/samitheberber
			 */

/*!
			 * numbro.js language configuration
			 * language : French
			 * locale: Canada
			 * author : Léo Renaud-Allaire : https://github.com/renaudleo
			 */

/*!
			 * numbro.js language configuration
			 * language : French
			 * locale: France
			 * author : Adam Draper : https://github.com/adamwdraper
			 */

/*!
			 * numbro.js language configuration
			 * language : French
			 * locale: Switzerland
			 * author : Adam Draper : https://github.com/adamwdraper
			 */

/*!
			 * numbro.js language configuration
			 * language : German
			 * locale: Austria
			 * author : Tim McIntosh (StayinFront NZ)
			 */

/*!
			 * numbro.js language configuration
			 * language : German
			 * locale: Germany
			 * author : Marco Krage : https://github.com/sinky
			 *
			 * Generally useful in Germany, Austria, Luxembourg, Belgium
			 */

/*!
			 * numbro.js language configuration
			 * language : German
			 * locale: Liechtenstein
			 * author : Michael Piefel : https://github.com/piefel (based on work from Marco Krage : https://github.com/sinky)
			 */

/*!
			 * numbro.js language configuration
			 * language : German
			 * locale: Switzerland
			 * author : Michael Piefel : https://github.com/piefel (based on work from Marco Krage : https://github.com/sinky)
			 */

/*!
			 * numbro.js language configuration
			 * language : Greek (el)
			 * author : Tim McIntosh (StayinFront NZ)
			 */

/*!
			 * numbro.js language configuration
			 * language : Hebrew
			 * locale : IL
			 * author : Eli Zehavi : https://github.com/eli-zehavi
			 */

/*!
			 * numbro.js language configuration
			 * language : Hungarian
			 * locale: Hungary
			 * author : Peter Bakondy : https://github.com/pbakondy
			 */

/*!
			 * numbro.js language configuration
			 * language : Indonesian
			 * author : Tim McIntosh (StayinFront NZ)
			 */

/*!
			 * numbro.js language configuration
			 * language : Italian
			 * locale: Italy
			 * author : Giacomo Trombi : http://cinquepunti.it
			 */

/*!
			 * numbro.js language configuration
			 * language : Italian
			 * locale: Switzerland
			 * author : Tim McIntosh (StayinFront NZ)
			 */

/*!
			 * numbro.js language configuration
			 * language : Japanese
			 * locale: Japan
			 * author : teppeis : https://github.com/teppeis
			 */

/*!
			 * numbro.js language configuration
			 * language : Korean
			 * author (numbro.js Version): Randy Wilander : https://github.com/rocketedaway
			 * author (numeral.js Version) : Rich Daley : https://github.com/pedantic-git
			 */

/*!
			 * numbro.js language configuration
			 * language : Latvian
			 * locale: Latvia
			 * author : Lauris Bukšis-Haberkorns : https://github.com/Lafriks
			 */

/*!
			 * numbro.js language configuration
			 * language : Norwegian Bokmål (nb)
			 * author : Tim McIntosh (StayinFront NZ)
			 */

/*!
			 * numbro.js language configuration
			 * language : Norwegian Nynorsk (nn)
			 * author : Tim McIntosh (StayinFront NZ)
			 */

/*!
			 * numbro.js language configuration
			 * language : Polish
			 * locale : Poland
			 * author : Dominik Bulaj : https://github.com/dominikbulaj
			 */

/*!
			 * numbro.js language configuration
			 * language : Portuguese
			 * locale : Brazil
			 * author : Ramiro letandas Jr : https://github.com/ramirovjr
			 */

/*!
			 * numbro.js language configuration
			 * language : Portuguese
			 * locale : Portugal
			 * author : Diogo Resende : https://github.com/dresende
			 */

/*!
			 * numbro.js language configuration
			 * language : Russian
			 * locale : Russsia
			 * author : Anatoli Papirovski : https://github.com/apapirovski
			 */

/*!
			 * numbro.js language configuration
			 * language : Russian
			 * locale : Ukraine
			 * author : Anatoli Papirovski : https://github.com/apapirovski
			 */

/*!
			 * numbro.js language configuration
			 * language : Serbian (sr)
			 * country : Serbia (Cyrillic)
			 * author : Tim McIntosh (StayinFront NZ)
			 */

/*!
			 * numbro.js language configuration
			 * language : Slovak
			 * locale : Slovakia
			 * author : Jan Pesa : https://github.com/smajl (based on work from Ahmed Al Hafoudh : http://www.freevision.sk)
			 */

/*!
			 * numbro.js language configuration
			 * language : Slovene
			 * locale: Slovenia
			 * author : Tim McIntosh (StayinFront NZ)
			 */

/*!
			 * numbro.js language configuration
			 * language : Spanish
			 * locale: Argentina
			 * author : Hernan Garcia : https://github.com/hgarcia
			 */

/*!
			 * numbro.js language configuration
			 * language : Spanish
			 * locale: Chile
			 * author : Gwyn Judd : https://github.com/gwynjudd
			 */

/*!
			 * numbro.js language configuration
			 * language : Spanish
			 * locale: Colombia
			 * author : Gwyn Judd : https://github.com/gwynjudd
			 */

/*!
			 * numbro.js language configuration
			 * language : Spanish
			 * locale: Costa Rica
			 * author : Gwyn Judd : https://github.com/gwynjudd
			 */

/*!
			 * numbro.js language configuration
			 * language : Spanish
			 * locale: El Salvador
			 * author : Gwyn Judd : https://github.com/gwynjudd
			 */

/*!
			 * numbro.js language configuration
			 * language : Spanish
			 * locale: Mexico
			 * author : Joe Bordes : https://github.com/joebordes
			 */

/*!
			 * numbro.js language configuration
			 * language : Spanish
			 * locale: Nicaragua
			 * author : Gwyn Judd : https://github.com/gwynjudd
			 */

/*!
			 * numbro.js language configuration
			 * language : Spanish
			 * locale: Peru
			 * author : Gwyn Judd : https://github.com/gwynjudd
			 */

/*!
			 * numbro.js language configuration
			 * language : Spanish
			 * locale: Puerto Rico
			 * author : Gwyn Judd : https://github.com/gwynjudd
			 */

/*!
			 * numbro.js language configuration
			 * language : Spanish
			 * locale: Spain
			 * author : Hernan Garcia : https://github.com/hgarcia
			 */

/*!
			 * numbro.js language configuration
			 * language : Swedish
			 * locale : Sweden
			 * author : Benjamin Van Ryseghem (benjamin.vanryseghem.com)
			 */

/*!
			 * numbro.js language configuration
			 * language : Thai
			 * locale : Thailand
			 * author : Sathit Jittanupat : https://github.com/jojosati
			 */

/*!
			 * numbro.js language configuration
			 * language : Turkish
			 * locale : Turkey
			 * author : Ecmel Ercan : https://github.com/ecmel,
			 *          Erhan Gundogan : https://github.com/erhangundogan,
			 *          Burak Yiğit Kaya: https://github.com/BYK
			 */

/*!
			 * numbro.js language configuration
			 * language : Ukrainian
			 * locale : Ukraine
			 * author : Michael Piefel : https://github.com/piefel (with help from Tetyana Kuzmenko)
			 */

/*!
			 * numbro.js language configuration
			 * language : simplified chinese
			 * locale : China
			 * author : badplum : https://github.com/badplum
			 */

/*!
			 * numbro.js language configuration
			 * language: Norwegian Bokmål
			 * locale: Norway
			 * author : Benjamin Van Ryseghem
			 */

/*!
			 * numeral.js language configuration
			 * language : Romanian
			 * author : Andrei Alecu https://github.com/andreialecu
			 */

/*!
			+ * numbro.js language configuration
			 * language : English
			 * locale: Ireland
			 * author : Tim McIntosh (StayinFront NZ)
			 */

/*!
	 * Copyright (c) 2017 Benjamin Van Ryseghem<<EMAIL>>
	 *
	 * Permission is hereby granted, free of charge, to any person obtaining a copy
	 * of this software and associated documentation files (the "Software"), to deal
	 * in the Software without restriction, including without limitation the rights
	 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
	 * copies of the Software, and to permit persons to whom the Software is
	 * furnished to do so, subject to the following conditions:
	 *
	 * The above copyright notice and this permission notice shall be included in
	 * all copies or substantial portions of the Software.
	 *
	 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
	 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
	 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
	 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
	 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
	 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
	 * SOFTWARE.
	 */

/*!
	autosize 4.0.4
	license: MIT
	http://www.jacklmoore.com/autosize
*/

/*!
 * @overview es6-promise - a tiny implementation of Promises/A+.
 * @copyright Copyright (c) 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors (Conversion to ES6 API by Jake Archibald)
 * @license   Licensed under MIT license
 *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
 * @version   v4.2.8+1e68dce6
 */

/*!
 * JavaScript Cookie v2.2.1
 * https://github.com/js-cookie/js-cookie
 *
 * Copyright 2006, 2015 Klaus Hartl & Fagner Brack
 * Released under the MIT license
 */

/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */

/*!
 * vuex v4.1.0
 * (c) 2022 Evan You
 * @license MIT
 */

/*!
* focus-trap 7.5.4
* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
*/

/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/

/*! #__NO_SIDE_EFFECTS__ */

/*! Hammer.JS - v2.0.7 - 2016-04-22
 * http://hammerjs.github.io/
 *
 * Copyright (c) 2016 Jorik Tangelder;
 * Licensed under the MIT license */

/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */

/**
 * @license
 * Copyright (c) 2014 The Polymer Project Authors. All rights reserved.
 * This code may only be used under the BSD style license found at http://polymer.github.io/LICENSE.txt
 * The complete set of authors may be found at http://polymer.github.io/AUTHORS.txt
 * The complete set of contributors may be found at http://polymer.github.io/CONTRIBUTORS.txt
 * Code distributed by Google as part of the polymer project is also
 * subject to an additional IP rights grant found at http://polymer.github.io/PATENTS.txt
 */

/**
* @vue/shared v3.4.19
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
