<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\Attributes\On;
use App\Models\Configuration;
use Livewire\Attributes\Session;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;

class Stepper extends Component
{
    public $step;
    public $isNextButtonEnabled = false;
    public Configuration $config;

    #[Session(key: 'lang')]
    public string $lang;

    public function mount()
    {
        $this->lang = session('lang', App::getLocale());

        $routeName = Route::currentRouteName();

        switch ($routeName) {
            case 'home':
            $this->step = 1;
            break;

            case 'type-assortments':
            $this->step = 2;
            break;

            case 'modules':
            $this->step = 3;
            
            break;

            case 'accessories':
            $this->step = 4;
            $this->isNextButtonEnabled = true;
            break;

            case 'summary':
            $this->step = 5;
            break;

            default:
            $this->step = 0; // Default step if route doesn't match
            break;
        }
    }

    public function render()
    {
        App::setLocale($this->lang);

        return view('livewire.stepper');
    }

    // Go to next step
    public function nextStep()
    {
        switch ($this->step) {
            case 1:
                $this->dispatch('create-config');
            break;

            case 2:
                $this->dispatch('goto-modules');
            break;

            case 3:
                // Check if one drawer has free space to add a module and show modal
                if ($this->config->hasFreeSpace()) {
                    $this->dispatch('show-slots-available-modal', $this->config);
                } else {
                    $this->dispatch('goto-accessories');
                }
            break;

            case 4:
                $this->dispatch('goto-summary');
            break;

            default:
                redirect()->route('home');
            break;
        }
    }

    // Go to previous step
    public function previousStep()
    {
        switch ($this->step) {
            case 2:
                $this->redirect(route('home'), navigate: true);
            break;

            case 3:
                $this->redirect(route('type-assortments', ['uuid' => $this->config->uuid]), navigate: true);
            break;

            case 4:
                $this->redirect(route('modules', ['uuid' => $this->config->uuid]), navigate: true);
            break;

            case 5:
                $this->redirect(route('accessories', ['uuid' => $this->config->uuid]), navigate: true);
            break;

            default:
                redirect()->route('home');
            break;
        }
    }

    /**
     * Restart configuration
     */
    public function resetConfig()
    {
        $this->dispatch('reset-config');
    }

    /**
     * Toggle next button
     */
    #[On('set-next-button-status')]
    public function setNextButtonStatus(bool $status): void
    {
        $this->isNextButtonEnabled = $status;
    }

    /**
     * Load configuration if exixts
     */
    #[On('load-config')]
    public function loadConfig(Configuration $config): void
    {
        $this->config = $config;
    }

    public function showSummaryDetails(): void
    {
        $this->dispatch('show-summary-details', $this->config->uuid);
    }
}
