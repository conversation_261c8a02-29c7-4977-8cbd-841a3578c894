<?php

namespace App\Livewire\Pages;

use Livewire\Component;
use App\Exports\ConfigExport;
use App\Models\Configuration;
use Livewire\Attributes\Title;
use Barryvdh\DomPDF\Facade\Pdf;
use Livewire\Attributes\Session;
use Livewire\Attributes\Validate;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Storage;

class Summary extends Component
{
    public string $title;

    #[Session(key: 'lang')]
    public string $lang;

    public Configuration $config;

    #[Validate('required', 'email')]
    public string $email;

    #[Validate('accepted')]
    public bool $acceptance;

    public function mount($uuid)
    {
        $this->lang = session('lang', App::getLocale());

        // Load the configuration
        $this->config = Configuration::where('uuid', $uuid)->firstOrFail();
        // Send the configuration data to the header component
        $this->dispatch('load-config', $this->config);

        $this->title = trans('Summary - Beta Utensili');
    }

    public function render()
    {
        App::setLocale($this->lang);

        return view('livewire.pages.summary')->title($this->title);
    }

    /**
     * The following methods is used to pass to the main website database the user's data.
     */
    public function quickCheckout(): void
    {
        $this->validate();

        $data = [
            // Auth token from env file
            'auth_token' => config('app.beta_tools.auth_token'),
            // User data
            'email' => $this->email,
            'nome' => $this->config->product->name,
            'descrizione' => $this->config->product->getTitleLabelAttribute($this->lang),
            'codice_colore' => '',
            'nome_colore' => $this->config->variant->ral_code,
            'link_configurazione' => $this->config->link,
            'url_immagine' => config('app.url') . Storage::url($this->config->variant->image),
        ];
        $response = Http::asForm()->withHeaders([
            'Accept' => 'application/json',
        ]);
        if (app()->environment(['local', 'staging'])) {
            $response = $response->withBasicAuth(config('app.beta_tools.basic_auth.username'), config('app.beta_tools.basic_auth.password'));
        }

        // Send the data to the main website as a POST request
        $response = $response->post(config('app.beta_tools.url') . '/' . $this->lang . '/configuratorecassettiere/api/toemail', $data);

        if ($response->successful()) {
            // Handle successful response
            session()->flash('success', 'Configuration successfully saved.');
            Log::info('User data sent to the main website. Response: ' . $response->body() . ' Status: ' . $response->status());
            
            // Push event to dataLayer for GTM tracking
            $this->dispatch('pushToDataLayer', title: 'newsletter_signup_success');
        } else {
            // Handle error response
            session()->flash('error', 'Error, try again.');
            Log::info('Error. Response: ' . $response->body() . ' Status: ' . $response->status());
        }
    }

    /**
     * The following methods is used to redirect the user to the main website, for login or registration.
     */
    public function goToMyBeta()
    {
        $data = [
            // Configuration data
            'nome' => $this->config->product->name,
            'descrizione' => $this->config->product->getTitleLabelAttribute($this->lang),
            'codice_colore' => '',
            'nome_colore' => $this->config->variant->ral_code,
            'link_configurazione' => $this->config->link,
            'url_immagine' => config('app.url') . Storage::url($this->config->variant->image),
        ];

        // Build the query string
        $query = http_build_query($data);
        $url = config('app.beta_tools.mybeta_redirect') . '/' . $this->lang . '/configuratorecassettiere/api/tomybeta' . "?$query";

        Log::info('Redirect to MyBeta. URL: ' . $url);

        // Redirect the user to the main website for login or registration to My Beta
        return redirect()->away($url);
    }

    public function downloadExcel()
    {
        // Export the configuration data to an Excel file
        $fileName = $this->config->uuid . '.xlsx';
        return Excel::download(new ConfigExport($this->config), $fileName);
    }

    public function downloadPDF()
    {
        // Export the configuration data to a PDF file
        $fileName = $this->config->uuid . '.pdf';
        $isRemoteEnabled = app()->environment('production') ? true : false;
        
        $this->config->load([
            'drawers.modules', 
            'assortment', 
            'product', 
            'variant',
            'accessories'
        ]);
        
        $pdf = Pdf::setOption('isRemoteEnabled', $isRemoteEnabled)->loadView('exports.configuration.pdf', [
            'config' => $this->config,
            'lang' => $this->lang,
        ]);
        
        return response()->streamDownload(function () use ($pdf) {
            echo $pdf->stream();
        }, $fileName);
    }
}
