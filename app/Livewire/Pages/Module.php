<?php

namespace App\Livewire\Pages;

use App\Models\Drawer;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Url;
use App\Models\Configuration;
use Livewire\Attributes\Title;
use Livewire\Attributes\Session;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use App\Models\Module as ModuleModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Livewire\Attributes\Computed;

class Module extends Component
{
    public string $title;

    #[Session(key: 'lang')]
    public string $lang;

    public Configuration $config;

    public Drawer $selectedDrawer;

    #[Url(as: 's', except: '')]
    public $search = '';

    #[Url(as: 'f', except: [])]
    public $filters = [];

    private $orientationFilter = '';

    public function mount($uuid)
    {
        $this->lang = session('lang', App::getLocale());

        // Load the configuration
        $this->config = Configuration::where('uuid', $uuid)->firstOrFail();
        // Send the configuration data to the header component
        $this->dispatch('load-config', $this->config);

        $this->selectedDrawer = $this->config->drawers->first();

        $this->title = trans('Modules - Beta Utensili');
    }

    public function render()
    {
        App::setLocale($this->lang);

        // Set the status of Next button to true if at least one module is added to one drawer
        if ($this->config->drawers->pluck('modules')->flatten()->count() > 0) {
            $this->dispatch('set-next-button-status', true);
        }
        else {
            $this->dispatch('set-next-button-status', false);
        }

        // Set the orientation filter to only show module with the same orientation of the first module in the drawer
        if ($this->selectedDrawer->modules()->first() && $this->selectedDrawer->modules()->first()->exists()) {
            $this->orientationFilter = $this->config->product->modules()->where('modules.id', $this->selectedDrawer->modules()->first()->id)->first()->pivot->orientation;
        }
        else {
            $this->orientationFilter = '';
        }

        // Fetch modules and available filters (without duplicates)
        $modules = $this->fetchModules();

        // Fetch available filters in UNIQUE mode
        $availableFilters = $this->config->product->getModulesFilterItemsAttribute(true, $this->config->modules_material, $this->orientationFilter);

        return view('livewire.pages.module', [
            'modules' => $modules,
            'availableFilters' => $availableFilters,
        ])->title($this->title);
    }

    public function resetSearch()
    {
        $this->search = '';
    }

    public function resetFilters()
    {
        $this->filters = [];
    }

    public function selectDrawer($id): void
    {
        // Close details modal
        $this->dispatch('close-modal');

        // Set the selected drawer
        $this->selectedDrawer = $this->config->drawers->where('id', $id)->first();
        
        // Reset search and filters
        $this->resetSearch();
        $this->resetFilters();
    }

    /**
     * Clear the selected drawer
     */
    public function clearSelectedDrawerModules(): void
    {
        if ($this->config->assortment) {
            $totalModulesFromAssortment = $this->selectedDrawer->getTotalModulesFromAssortment();
            $modules = $this->selectedDrawer->modules()->orderBy('position', 'asc')->get();

            foreach ($modules as $index => $module) {
                if ($index >= $totalModulesFromAssortment) {
                    $this->selectedDrawer->modules()->detach();

                    if ($this->config->assortment) {
                        $preconfiguredDrawer = $this->config->assortment->preconfiguredDrawers->where('position', $this->selectedDrawer->position)->first();
                        if ($preconfiguredDrawer) {
                            foreach ($preconfiguredDrawer->modules as $module) {
                                $this->selectedDrawer->modules()->attach($module->id, [
                                    'position' => $module->pivot->position,
                                ]);
                                $this->selectedDrawer->locked = true;
                                $this->selectedDrawer->units = $this->selectedDrawer->getMaxUnits($this->config->product->getDrawerTemplateForPosition($this->selectedDrawer->position), $module->format);
                                $this->selectedDrawer->save();
                            }
                        }
                    }
                }
            }
        }
        else {
            // Clear the selected drawer modules
            $this->selectedDrawer->modules()->detach();

            // Unset the drawer units to the module format
            $this->selectedDrawer->units = null;
            $this->selectedDrawer->save();
        }
    }

    // Clear a specific module from the selected drawer
    #[On('clear-module')]
    public function clearModule($pivotId): void
    {
        $this->selectedDrawer->modules()->newPivotQuery()->where('id', $pivotId)->delete();
    }

    /**
     *  Show the "Already Inserted" message on module.
     *
     * @param int $id
     */
    public function isAlreadyInserted($id): bool
    {
        // Check if the module is already inserted in the selectedDrawer
        return $this->config->drawers->pluck('modules')->flatten()->contains('id', $id);
    }

    /**
     * Get the orientation of the module in the selected drawer
     *
     */
    public function getDrawerOrientation(): string
    {
        // Get the module orientation in the selected drawer
        return $this->orientationFilter;
    }

    /**
     * Insert a module
     *
     * @param int $id
     */
    public function addModule($id): void
    {
        // Retrive the module to add
        $module = $this->config->product->modules()->findOrFail($id);

        // Set the drawer units to the module format
        $this->selectedDrawer->units = $this->selectedDrawer->getMaxUnits($this->config->product->getDrawerTemplateForPosition($this->selectedDrawer->position), $module->format);
        $this->selectedDrawer->save();

        // Set the position of the module in the drawer
        $position = $this->selectedDrawer->modules()->where('drawer_module.position', '>', 0)->count() + 1;

        // Attach the module to the drawer in the selected position
        $this->selectedDrawer->modules()->attach($module, ['position' => $position]);

        // Check if drawer is MM1/MM2 and module added is STD -> add mandatory filler modules (only first time a model is added)
        if ($this->config->product->drawer_template == 'mm1') {
            if ($this->selectedDrawer->modules()->count() == 1) {
                if ($module->type == 'std4' && $module->material == 'soft') {
                    $fillerModuleHorizontal = ModuleModel::query()->where('code', 'VPM8')->first();
                    $fillerModuleVertical = ModuleModel::query()->where('code', 'VPM7-C')->first();

                    $this->selectedDrawer->modules()->attach($fillerModuleHorizontal, ['position' => 0]);
                    $this->selectedDrawer->modules()->attach($fillerModuleVertical, ['position' => 0]);
                }
                elseif ($module->type == 'std4' && $module->material == 'rigid') {
                    $fillerModuleHorizontal = ModuleModel::query()->where('code', 'VP-LS')->first();
                    // $fillerModuleVertical = ModuleModel::query()->where('code', 'VPM7-C')->first();

                    $this->selectedDrawer->modules()->attach($fillerModuleHorizontal, ['position' => 0]);
                    // $this->selectedDrawer->modules()->attach($fillerModuleVertical, ['position' => 0]);
                }
            }
        }
        elseif ($this->config->product->drawer_template == 'mm2') {
            if ($this->selectedDrawer->modules()->count() == 1) {
                if ($module->type == 'std4' && $module->material == 'soft') {
                    $fillerModuleHorizontal = ModuleModel::query()->where('code', 'VPM8')->first();
                    $fillerModuleVertical = ModuleModel::query()->where('code', 'VPM7')->first();

                    $this->selectedDrawer->modules()->attach($fillerModuleHorizontal, ['position' => 0]);
                    $this->selectedDrawer->modules()->attach($fillerModuleVertical, ['position' => 0]);
                }
                elseif ($module->type == 'std4' && $module->material == 'rigid') {
                    $fillerModuleHorizontal = ModuleModel::query()->where('code', 'VP-LS')->first();
                    $fillerModuleVertical = ModuleModel::query()->where('code', 'VP-3SC')->first();

                    $this->selectedDrawer->modules()->attach($fillerModuleHorizontal, ['position' => 0]);
                    $this->selectedDrawer->modules()->attach($fillerModuleVertical, ['position' => 0]);
                }
            }
        }

        // Reset the filters
        $this->resetFilters();
        $this->resetSearch();
    }

    /**
     * Insert a module
     *
     * @param int $id
     */
    #[On('add-module')]
    public function addModuleEvent($id): void
    {
        $this->addModule($id);
    }

    /**
     * Fetch modules
     *
     * @return Collection
     */
    private function fetchModules(): Collection
    {
        // Fetch available filters (with duplicates because we need each ID for the query)
        $filters = $this->config->product->getModulesFilterItemsAttribute(false, '', '');

        // Calculate the free units in the selected drawer
        if ($this->selectedDrawer->units) {
            $freeUnits = $this->selectedDrawer->units - $this->selectedDrawer->getUsedUnits();
        }
        else {
            $freeUnits = null;
        }

        // if ($this->selectedDrawer->isLocked()) {
        //     $freeUnits = 0;
        // }

        return $this->config->product->modules()
            // Select only modules that have the config material type
            ->where('material', $this->config->modules_material)
            // Apply search argument
            ->when($this->search, function (Builder $query) {
                return $query->search($this->search);
            })
            // Apply filters
            ->when($this->filters, function (Builder $query) use ($filters) {
                $filterIds = collect($this->filters)->flatMap(function ($code) use ($filters) {
                    return $filters->where('code', $code)->pluck('id');
                })->all();

                return $query->whereIn('modules.id', $filterIds);
            })
            // Apply orientation filter if set from the system
            ->when($this->orientationFilter, function (Builder $query) {
                return $query->where('module_product.orientation', $this->orientationFilter);
            })
            // Apply free units filter to show only modules that can be inserted in the drawer
            ->when(!is_null($freeUnits), function (Builder $query) use ($freeUnits) {
                return $query->where('units', '<=', $freeUnits);
            })
            ->get();
    }

    public function showModuleDetails(ModuleModel $module): void
    {
        $this->dispatch('show-module-details', $module->id);
    }

    /**
     * Redirect the user to the next step of the configuration
     *
     */
    #[On('goto-accessories')]
    public function next(): void
    {
        $this->redirect(route('accessories', ['uuid' => $this->config->uuid]), navigate: true);
    }

    // Update position of modules in the drawer
    #[On('update-position')]
    public function updatePosition($item, $position): void
    {
        $module = ModuleModel::findOrFail($item);

        $this->moveModule($module, $position + 1);
    }

    /**
     * Move a module to a new position
     *
     * @param ModuleModel $module
     * @param int $position
     */
    protected function moveModule($module, $position)
    {
        // Ottieni la posizione corrente
        $current = $this->selectedDrawer->modules()->where('modules.id', $module->id)->first()->pivot->position;
        $after = $position;

        if ($current === $after) return;

        // Aggiorna la posizione temporaneamente a -1
        $this->selectedDrawer->modules()->updateExistingPivot($module->id, ['position' => -1]);

        // Seleziona i moduli da spostare
        $block = $this->selectedDrawer->modules()->wherePivotBetween('position', [
            min($current, $after),
            max($current, $after)
        ]);

        $needToShiftBlockUpBecauseDraggingTargetDown = $current < $after;

        // Sposta gli altri moduli
        if ($needToShiftBlockUpBecauseDraggingTargetDown) {
            $block->get()->each(function($item) {
                $this->selectedDrawer->modules()->updateExistingPivot($item->id, [
                    'position' => $item->pivot->position - 1
                ]);
            });
        } else {
            $block->get()->each(function($item) {
                $this->selectedDrawer->modules()->updateExistingPivot($item->id, [
                    'position' => $item->pivot->position + 1
                ]);
            });
        }

        // Imposta la posizione finale
        $this->selectedDrawer->modules()->updateExistingPivot($module->id, ['position' => $after]);
    }
}
