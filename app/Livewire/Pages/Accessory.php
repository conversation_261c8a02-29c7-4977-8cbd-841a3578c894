<?php

namespace App\Livewire\Pages;

use App\Models\Accessory as ModelsAccessory;
use Livewire\Component;
use Livewire\Attributes\On;
use App\Models\Configuration;
use Livewire\Attributes\Title;
use Livewire\Attributes\Session;
use Livewire\Attributes\Validate;
use Illuminate\Support\Facades\App;

class Accessory extends Component
{
    public string $title;

    #[Session(key: 'lang')]
    public string $lang;

    public Configuration $config;

    #[Validate('required')]
    public $selectedAccessories = [];

    public function mount($uuid)
    {
        $this->lang = session('lang', App::getLocale());

        // Load the configuration
        $this->config = Configuration::where('uuid', $uuid)->firstOrFail();
        // Send the configuration data to the header component
        $this->dispatch('load-config', $this->config);
        // Load accessories from the configuration if they are already selected
        $this->selectedAccessories = $this->config->accessories->pluck('id')->toArray();

        $this->title = trans('Accessories - Beta Utensili');
    }

    public function render()
    {
        App::setLocale($this->lang);

        return view('livewire.pages.accessory', [
            'accessories' => $this->config->product->accessories,
        ])->title($this->title);
    }

        /**
     * Insert an accessory in the configuration
     *
     * @param int $id
     */
    #[On('add-accessory')]
    public function addModuleEvent($id): void
    {
        $this->selectedAccessories[] = $id;
    }

    /**
     * Redirect the user to the next step of the configuration
     *
     */
    #[On('goto-summary')]
    public function next(): void
    {
        // Save the selected accessories
        $this->config->accessories()->sync($this->selectedAccessories);

        // Redirect to the summary page
        $this->redirect(route('summary', ['uuid' => $this->config->uuid]), navigate: true);
    }

    public function showAccessoryDetails(ModelsAccessory $accessory): void
    {
        $this->dispatch('show-accessory-details', $accessory->id);
    }
}
