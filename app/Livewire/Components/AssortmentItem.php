<?php

namespace App\Livewire\Components;

use Livewire\Component;
use App\Models\Assortment;
use Livewire\Attributes\Session;
use Livewire\Attributes\Reactive;
use Illuminate\Support\Facades\App;

class AssortmentItem extends Component
{
    #[Session(key: 'lang')]
    public string $lang;
    
    #[Reactive]
    public Assortment $assortment;

    #[Reactive]
    public int $drawersCount;

    public function mount(Assortment $assortment, int $drawersCount)
    {
        $this->lang = session('lang', App::getLocale());

        $this->assortment = $assortment;
        $this->drawersCount = $drawersCount;
    }

    public function render()
    {
        App::setLocale($this->lang);
        
        return view('livewire.components.assortment-item');
    }

    public function selectAssortment(): void
    {
        $this->dispatch('select-assortment', $this->assortment);
    }

    public function showAssortmentDetails(Assortment $assortment): void
    {
        $this->dispatch('show-assortment-details', $assortment->id);
    }
}
