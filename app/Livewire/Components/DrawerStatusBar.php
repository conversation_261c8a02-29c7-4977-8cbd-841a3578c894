<?php

namespace App\Livewire\Components;

use App\Models\Drawer;
use Livewire\Component;
use Livewire\Attributes\Session;
use Livewire\Attributes\Reactive;
use Illuminate\Support\Facades\App;

class DrawerStatusBar extends Component
{
    #[Session(key: 'lang')]
    public string $lang;
    
    #[Reactive]
    public Drawer $drawer;

    #[Reactive]
    public bool $isFull;
    
    #[Reactive]
    public int $usedUnits;
    
    #[Reactive]
    public int $availableUnits;

    public function mount(Drawer $drawer, bool $isFull, int $usedUnits, int $availableUnits)
    {
        $this->lang = session('lang', App::getLocale());

        $this->drawer = $drawer;
        $this->isFull = $isFull;
        $this->usedUnits = $usedUnits;
        $this->availableUnits = $availableUnits;
    }

    public function render()
    {
        App::setLocale($this->lang);
        
        return view('livewire.components.drawer-status-bar');
    }
}
