<?php

namespace App\Livewire\Components;

use App\Models\Product;
use App\Models\Variant;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Session;
use Illuminate\Support\Facades\App;

class VariantItem extends Component
{
    #[Session(key: 'lang')]
    public string $lang;
    
    public Product $product;
    public Variant $variant;

    public $selectedProduct;
    public $selectedVariant;

    public function mount(Product $product, Variant $variant)
    {
        $this->lang = session('lang', App::getLocale());
        
        $this->product = $product;
        $this->variant = $variant;
    }

    public function render()
    {
        App::setLocale($this->lang);
        
        return view('livewire.components.variant-item');
    }

    #[On('selected-variant.{variant.id}')]
    public function setSelectedVariant(Product $product, Variant $variant): void
    {
        $this->selectedVariant = $variant;
        $this->selectedProduct = $product;
    }

    public function selectVariant(): void
    {
        $this->dispatch('select-variant', $this->product, $this->variant);
    }
}
