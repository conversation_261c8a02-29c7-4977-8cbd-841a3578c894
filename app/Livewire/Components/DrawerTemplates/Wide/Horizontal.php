<?php

namespace App\Livewire\Components\DrawerTemplates\Wide;

use Livewire\Component;
use Livewire\Attributes\Reactive;

class Horizontal extends Component
{
    #[Reactive]
    public $modules;

    public $drawer;

    public function render()
    {
        return view('livewire.components.drawer-templates.wide.horizontal');
    }

    public function updatePosition($item, $position)
    {
        $this->dispatch('update-position', $item, $position);

        return $this->skipRender();
    }

    public function clearModule($pivotId): void
    {
        $this->dispatch('clear-module', $pivotId);
    }
}
