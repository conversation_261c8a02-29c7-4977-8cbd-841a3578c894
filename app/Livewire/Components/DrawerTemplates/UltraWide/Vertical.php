<?php

namespace App\Livewire\Components\DrawerTemplates\UltraWide;

use Livewire\Component;
use Livewire\Attributes\Reactive;

class Vertical extends Component
{
    #[Reactive]
    public $modules;

    public $drawer;

    public function render()
    {
        return view('livewire.components.drawer-templates.ultra-wide.vertical');
    }

    public function updatePosition($item, $position)
    {
        $this->dispatch('update-position', $item, $position);

        return $this->skipRender();
    }

    public function clearModule($pivotId): void
    {
        $this->dispatch('clear-module', $pivotId);
    }
}
