<?php

namespace App\Livewire\Components\DrawerTemplates\Mm1;

use Livewire\Component;
use Livewire\Attributes\Reactive;

class Standard extends Component
{
    #[Reactive]
    public $modules;

    #[Reactive]
    public $material;

    public $drawer;

    public function render()
    {
        return view('livewire.components.drawer-templates.mm1.standard');
    }

    public function updatePosition($item, $position)
    {
        $this->dispatch('update-position', $item, $position);

        return $this->skipRender();
    }

    public function clearModule($pivotId): void
    {
        $this->dispatch('clear-module', $pivotId);
    }
}
