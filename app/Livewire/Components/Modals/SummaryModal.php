<?php

namespace App\Livewire\Components\Modals;

use Livewire\Component;
use Livewire\Attributes\On;
use App\Models\Configuration;
use Livewire\Attributes\Session;
use Illuminate\Support\Facades\App;

class SummaryModal extends Component
{
    #[Session(key: 'lang')]
    public string $lang;

    public Configuration $config;

    public $showModal = false;

    public function mount()
    {
        $this->lang = session('lang', App::getLocale());
    }

    public function render()
    {
        App::setLocale($this->lang);

        return view('livewire.components.modals.summary-modal');
    }

    #[On('show-summary-details')]
    public function showModalDetails($uuid)
    {
        // Load the configuration
        $this->config = Configuration::where('uuid', $uuid)->firstOrFail();

        $this->showModal = true;
    }

    #[On('close-modal')]
    public function closeModal()
    {
        $this->showModal = false;
    }
}
