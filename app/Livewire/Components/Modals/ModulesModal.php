<?php

namespace App\Livewire\Components\Modals;

use App\Models\Module;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Session;
use Illuminate\Support\Facades\App;

class ModulesModal extends Component
{
    #[Session(key: 'lang')]
    public string $lang;
    
    public $module;

    public $components;

    public $showModal = false;

    public function mount()
    {
        $this->lang = session('lang', App::getLocale());
    }

    public function render()
    {
        App::setLocale($this->lang);

        return view('livewire.components.modals.modules-modal');
    }

    public function addModule(Module $module)
    {
        $this->dispatch('add-module', $module->id);
        $this->closeModal();
    }

    #[On('show-module-details')]
    public function showModalDetails(Module $module)
    {
        $this->module = $module;
        $this->components = $this->module->components;

        $this->showModal = true;
    }

    #[On('close-modal')]
    public function closeModal()
    {
        $this->showModal = false;
    }
}
