<?php

namespace App\Livewire\Components\Modals;

use Livewire\Component;
use App\Models\Accessory;
use Livewire\Attributes\On;
use Livewire\Attributes\Session;
use Illuminate\Support\Facades\App;

class AccessoriesModal extends Component
{
    #[Session(key: 'lang')]
    public string $lang;

    public $accessory;

    public $showModal = false;

    public function mount()
    {
        $this->lang = session('lang', App::getLocale());
    }

    public function render()
    {
        App::setLocale($this->lang);

        return view('livewire.components.modals.accessories-modal');
    }

    public function addAccessory(Accessory $accessory)
    {
        $this->dispatch('add-accessory', $accessory->id);
        $this->closeModal();
    }

    #[On('show-accessory-details')]
    public function showModalDetails(Accessory $accessory)
    {
        $this->accessory = $accessory;

        $this->showModal = true;
    }

    #[On('close-modal')]
    public function closeModal()
    {
        $this->showModal = false;
    }
}
