<?php

namespace App\Livewire\Components\Modals;

use App\Models\Configuration;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Session;
use Illuminate\Support\Facades\App;

class SlotsAvailableModal extends Component
{
    #[Session(key: 'lang')]
    public string $lang;

    public Configuration $config;

    public $showModal = false;
    
    public function mount()
    {
        $this->lang = session('lang', App::getLocale());
    }

    public function render()
    {
        App::setLocale($this->lang);
        
        return view('livewire.components.modals.slots-available-modal');
    }
    
    #[On('show-slots-available-modal')]
    public function showModalDetails(Configuration $config)
    {
        $this->config = $config;

        $this->showModal = true;
    }

    #[On('close-modal')]
    public function closeModal()
    {
        $this->showModal = false;
    }

    #[On('confitm-to-continue')]
    public function confirmToContinue()
    {
        $this->dispatch('goto-accessories');
    }
}
