<?php

namespace App\Livewire\Components\Modals;

use Livewire\Component;
use App\Models\Assortment;
use Livewire\Attributes\On;
use Livewire\Attributes\Session;
use Illuminate\Support\Facades\App;

class AssortmentsModal extends Component
{
    #[Session(key: 'lang')]
    public string $lang;
    
    public $assortment;

    public $modules;

    public $showModal = false;

    public function mount()
    {
        $this->lang = session('lang', App::getLocale());
    }

    public function render()
    {
        App::setLocale($this->lang);

        return view('livewire.components.modals.assortments-modal');
    }

    public function selectAssortment(Assortment $assortment)
    {
        $this->dispatch('select-assortment', $assortment->id);
        $this->closeModal();
    }

    #[On('show-assortment-details')]
    public function showModalDetails(Assortment $assortment)
    {
        $this->assortment = $assortment;
        $this->modules = $assortment->preconfiguredDrawers->flatMap->modules;
        $this->showModal = true;
    }

    #[On('close-modal')]
    public function closeModal()
    {
        $this->showModal = false;
    }
}
