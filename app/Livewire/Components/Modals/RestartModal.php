<?php

namespace App\Livewire\Components\Modals;

use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\Attributes\Session;
use Illuminate\Support\Facades\App;

class RestartModal extends Component
{
    #[Session(key: 'lang')]
    public string $lang;

    public $showModal = false;

    public function mount()
    {
        $this->lang = session('lang', App::getLocale());
    }
    
    public function render(): \Illuminate\View\View
    {
        App::setLocale($this->lang);
        
        return view('livewire.components.modals.restart-modal');
    }

    #[On('close-modal')]
    public function closeModal(): void
    {
        $this->showModal = false;
    }

    #[On('reset-config')]
    public function resetConfig(): void
    {
        $this->showModal = true;
    }

    public function restartConfiguration(): void
    {
        $this->showModal = false;
        $this->redirect(route('home'), navigate: true);
    }
}
