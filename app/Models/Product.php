<?php

namespace App\Models;

use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Product extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'active',
        'category_id',
        'featured',
        'code',
        'name',
        'reference',
        'title',
        'description',
        'image',
        'drawers',
        'drawer_template',
        'drawers_template',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'title' => 'array',
        'description' => 'array',
        'drawers_type' => 'array',
        'active' => 'boolean',
        'featured' => 'boolean',
        'drawers_template' => 'json',
    ];

    public function getTitleLabelAttribute($lang = 'en')
    {
        return $this->title[$lang] ?? $this->title['en'] ?? null;
    }

    public function getDescriptionLabelAttribute($lang = 'en')
    {
        return $this->description[$lang] ?? $this->description['en'] ?? null;
    }
    
    public function getModulesFilterItemsAttribute(bool $unique = false, string $selectedMaterial = '', string $orientationFilter = ''): Collection
    {
        $filters = collect();

        $this->modules->each(function ($module) use ($filters) {
            $units = $module->units;
            $format = $module->format;
            $orientation = $module->pivot->orientation;
            $type = $module->type;
            $material = $module->material;

            $filterItem = [
                'id' => $module->id,
                'code' => "{$type}-{$units}-{$format}",
                'name' => "{$units}/{$format}",
                'units' => $units,
                'format' => $format,
                'orientation' => $orientation,
                'type' => $type,
                'material' => $material,
            ];

            $filters->push($filterItem);
        });

        if (!empty($selectedMaterial)) {
            $filters = $filters->filter(function ($filter) use ($selectedMaterial) {
                return $filter['material'] === $selectedMaterial;
            });
        }

        // Remove filters that do not match the orientation
        if (!empty($orientationFilter)) {
            $filters = $filters->where('orientation', $orientationFilter);
        }

        // Remove duplicates - Non unique array is used for the query that returns the modules
        if ($unique) {
            $filters = $filters->unique('code')->values();
        }
        else {
            $filters = $filters->values();
        }

        $filters = $filters->sortBy(function ($filter) {
            return [$filter['format'], $filter['units']];
        });

        return $filters;
    }

    public function getDrawerTemplateForPosition(int $position): string
    {
        return $this->drawers_template[$position] ?? $this->drawer_template;
    }

    /**
     * The category that the product belongs to.
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the variants for the product.
     */
    public function variants()
    {
        return $this->hasMany(Variant::class);
    }

    /**
     * The assortments that belong to the product.
     */
    public function assortments()
    {
        return $this->belongsToMany(Assortment::class);
    }

    /**
     * The modules that belong to the product.
     */
    public function modules()
    {
        return $this->belongsToMany(Module::class)->withPivot('orientation');
    }

    /**
     * The accessories that belong to the product.
     */
    public function accessories()
    {
        return $this->belongsToMany(Accessory::class);
    }

    /**
     * The configurations that belong to the product.
     */
    public function configurations()
    {
        return $this->hasMany(Configuration::class);
    }

    /**
     * Scope a query to search products.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($query) use ($search) {
            $query->where('name', 'like', '%'.$search.'%')
                ->orWhere('reference', 'like', '%'.$search.'%')
                ->orWhere('title', 'like', '%'.$search.'%')
                ->orWhere('description', 'like', '%'.$search.'%')
                ->orWhere('code', 'like', '%'.$search.'%');
        })->orWhereHas('variants', function ($query) use ($search) {
            $query->where('name', 'like', '%'.$search.'%')
                ->orWhere('reference', 'like', '%'.$search.'%')
                ->orWhere('title', 'like', '%'.$search.'%')
                ->orWhere('description', 'like', '%'.$search.'%')
                ->orWhere('code', 'like', '%'.$search.'%')
                ->orWhere('ral_code', 'like', '%'.$search.'%')
                ->orWhere('swatch', 'like', '%'.$search.'%');
        });
    }
}
