<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Module extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'reference',
        'title',
        'description',
        'image',
        'type',
        'material',
        'units'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'title' => 'array',
        'description' => 'array',
    ];

    public function getTitleLabelAttribute($lang = 'en')
    {
        return $this->title[$lang] ?? $this->title['en'] ?? null;
    }

    public function getDescriptionLabelAttribute($lang = 'en')
    {
        return $this->description[$lang] ?? $this->description['en'] ?? null;
    }

    /**
     * Get the format for the module.
     */
    public function getFormatAttribute()
    {
        switch ($this->type) {
            case 'std4':
                return 4;
            case 'std3':
                return 3;
            case 'mm1':
                return 1;
            case 'mm2':
                return 1;
            default:
                return 0;
        }
    }

    /**
     * The products that belong to the module.
     */
    public function products()
    {
        return $this->belongsToMany(Product::class)->withPivot('orientation');
    }

    /**
     * The components that belong to the module.
     */
    public function components()
    {
        return $this->belongsToMany(Component::class);
    }

    /**
     * The drawers that belong to the module.
     */
    public function drawers()
    {
        return $this->belongsToMany(Drawer::class)->withPivot('id', 'position');
    }

    /**
     * The preconfigured drawers that belong to the module.
     */
    public function preconfiguredDrawers()
    {
        return $this->belongsToMany(PreconfiguredDrawer::class)->withPivot('position');
    }

    /**
     * Scope a query to search modules.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $search
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, $search)
    {
        return $query->where('name', 'like', '%'.$search.'%')
            ->orWhere('description', 'like', '%'.$search.'%')
            ->orWhere('code', 'like', '%'.$search.'%')
            ->orWhere('reference', 'like', '%'.$search.'%')
            ->orWhere('title', 'like', '%'.$search.'%');
    }
}
