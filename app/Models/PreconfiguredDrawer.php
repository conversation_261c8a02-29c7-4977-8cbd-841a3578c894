<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PreconfiguredDrawer extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'position',
        'orientation',
        'format',
        'assortment_id'
    ];

    /**
     * Get the assortment that owns the PreconfiguredDrawer
     */
    public function assortment()
    {
        return $this->belongsTo(Assortment::class);
    }

    /**
     * Get the modules for the PreconfiguredDrawer
     */
    public function modules()
    {
        return $this->belongsToMany(Module::class)->withPivot('position');
    }
}
