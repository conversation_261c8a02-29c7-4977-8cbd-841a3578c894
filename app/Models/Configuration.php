<?php

namespace App\Models;

use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Configuration extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'variant_id',
        'assortment_id',
        'uuid',
    ];

    /**
     * Get the configuration link attribute.
     */
    public function getLinkAttribute()
    {
        $link = route('summary', ['uuid' => $this->uuid]);
        return $link;
    }

    /**
     * Check if the configuration has free space in the drawers.
     */
    public function hasFreeSpace(): bool
    {
        $drawers = $this->drawers;
        $drawersWithFreeSpace = $drawers->filter(function ($drawer) {
            return !$drawer->isFull();
        });

        return $drawersWithFreeSpace->isNotEmpty();
    }

    /**
     * Get the configuration total number of pieces.
     */
    public function getTotalPiecesAttribute(): int
    {
        $totalPieces = 0;
        $drawers = $this->drawers;

        foreach ($drawers as $drawer) {
            foreach ($drawer->modules as $module) {
            $totalPieces += $module->components->sum('pieces');
            }
        }

        return $totalPieces;
    }

    /**
     * Get the product that owns the Configuration
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the variant that owns the Configuration
     */
    public function variant()
    {
        return $this->belongsTo(Variant::class);
    }

    /**
     * Get the accessories for the Configuration
     */
    public function accessories()
    {
        return $this->belongsToMany(Accessory::class);
    }

    /**
     * Get the assortment that owns the Configuration
     */
    public function assortment()
    {
        return $this->belongsTo(Assortment::class);
    }

    /**
     * Get all of the drawers for the Configuration
     */
    public function drawers()
    {
        return $this->hasMany(Drawer::class);
    }
}
