<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Variant extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'reference',
        'title',
        'description',
        'image',
        'swatch',
        'ral_code',
        'color_hex',
        'product_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'title' => 'array',
        'description' => 'array',
    ];

    public function getTitleLabelAttribute($lang = 'en')
    {
        return $this->title[$lang] ?? $this->title['en'] ?? null;
    }

    public function getDescriptionLabelAttribute($lang = 'en')
    {
        return $this->description[$lang] ?? $this->description['en'] ?? null;
    }

    /**
     * The product that belong to the variant.
     */
    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * The configurations that belong to the variant.
     */
    public function configurations()
    {
        return $this->hasMany(Configuration::class);
    }
}
