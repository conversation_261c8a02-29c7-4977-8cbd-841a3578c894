<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Assortment extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'code',
        'name',
        'reference',
        'title',
        'description',
        'image'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'title' => 'json',
        'description' => 'json',
    ];

    public function getTitleLabelAttribute($lang = 'en')
    {
        return $this->title[$lang] ?? $this->title['en'] ?? null;
    }

    public function getDescriptionLabelAttribute($lang = 'en')
    {
        return $this->description[$lang] ?? $this->description['en'] ?? null;
    }

    /**
     * The material the assortment's modules are made of.
     */
    public function getMaterialAttribute()
    {
        return $this->preconfiguredDrawers->first()->modules->first()->material;
    }

    /**
     * The sum of all the modules pieces.
     */
    public function getModulesPiecesAttribute()
    {
        return $this->preconfiguredDrawers->flatMap->modules->sum('pieces');
    }

    /**
     * The products that belong to the assortment.
     */
    public function products()
    {
        return $this->belongsToMany(Product::class);
    }

    /**
     * The configurations that belong to the assortment.
     */
    public function configurations()
    {
        return $this->hasMany(Configuration::class);
    }

    /**
     * The preconfigured drawers that belong to the assortment.
     */
    public function preconfiguredDrawers()
    {
        return $this->hasMany(PreconfiguredDrawer::class);
    }
}
