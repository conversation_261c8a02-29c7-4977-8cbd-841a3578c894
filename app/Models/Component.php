<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Component extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'reference',
        'title',
        'description',
        'image',
        'pieces',
    ];

    protected $casts = [
        'title' => 'json',
        'description' => 'json',
    ];

    public function getTitleLabelAttribute($lang = 'en')
    {
        return $this->title[$lang] ?? $this->title['en'] ?? null;
    }

    public function getDescriptionLabelAttribute($lang = 'en')
    {
        return $this->description[$lang] ?? $this->description['en'] ?? null;
    }

    /**
     * The modules that belong to the component.
     */
    public function modules()
    {
        return $this->belongsToMany(Module::class);
    }
}
