<?php

namespace App\Imports;

use App\Models\Product;
use App\Models\Variant;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class VariantsImport implements ToModel, WithChunkReading, WithBatchInserts, WithProgressBar, WithHeadingRow
{
    use Importable;
    
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        if (!isset($row['name'])) {
            return null;
        }

        // Check if the variant already exists.
        $variantExists = Variant::where('code', $row['name'])->exists();
        $variant = Variant::where('code', $row['name'])->first();

        // Get the product that the variant belongs to.
        $productExists = Product::where('code', $row['product_code'])->exists();
        $product = Product::where('code', $row['product_code'])->first();

        if ($variantExists) {
            if ($productExists) {
                // Update the existing variant
                $variant->update([
                    'code' => $row['name'],
                    'name' => $row['name'],
                    'reference' => $row['reference'],
                    'title' => null,
                    'description' => null,
                    'image' => 'images/variants/' . $row['image'],
                    'swatch' => $row['swatch'],
                    'ral_code' => $row['ral_code'],
                    'color_hex' => null,
                    'product_id' => $product->id,
                ]);
            }
            else {
                return null;
            }
        }
        else {
            if ($productExists) {
                return new Variant([
                    'code' => $row['name'],
                    'name' => $row['name'],
                    'reference' => $row['reference'],
                    'title' => null,
                    'description' => null,
                    'image' => 'images/variants/' . $row['image'],
                    'swatch' => $row['swatch'],
                    'ral_code' => $row['ral_code'],
                    'color_hex' => null,
                    'product_id' => $product->id,
                ]);
            }
            else {
                return null;
            }
        }
    }

    public function chunkSize(): int
    {
        return 10;
    }

    public function batchSize(): int
    {
        return 10;
    }
}
