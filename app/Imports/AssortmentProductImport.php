<?php

namespace App\Imports;

use App\Models\Product;
use App\Models\Assortment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class AssortmentProductImport implements ToCollection, WithChunkReading, WithBatchInserts, WithProgressBar, WithHeadingRow
{
    use Importable;
    
    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        foreach ($collection as $row) {
            if (!isset($row['product']) || !isset($row['assortment'])) {
                return null;
            }
            
            // Find the product and assortment by their names
            $product = Product::query()->where('name', $row['product'])->first();
            $assortment = Assortment::query()->where('name', $row['assortment'])->first();
    
            // If either the product or assortment doesn't exist, return null
            if (!$product || !$assortment) {
                Log::error('Product or assortment not found: ' . $row['product'] . ' - ' . $row['assortment']);
                return null;
            }
    
            // Check if the assortment is already attached to the product
            if (!$product->assortments()->where('assortment_id', $assortment->id)->exists()) {
                // Create a new pivot record only if it doesn't exist
                $product->assortments()->attach($assortment->id);
            }
        }
    }

    public function chunkSize(): int
    {
        return 10;
    }

    public function batchSize(): int
    {
        return 10;
    }
}
