<?php

namespace App\Imports;

use App\Models\Module;
use Cerbero\JsonParser\JsonParser;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class ModulesImport implements ToModel, WithChunkReading, WithBatchInserts, WithProgressBar, WithHeadingRow
{
    use Importable;
    
    /**
    * @param array $row
    *
    * @return \Illuminate\Database\Eloquent\Model|null
    */
    public function model(array $row)
    {
        if (!isset($row['name'])) {
            return null;
        }

        // Retrive the JSON object with the given name.
        $json = $this->findObjectByName($row['name']);

        // Extract the title from the JSON object.
        $title = [];
        if (isset($json['Title']) && is_array($json['Title'])) {
            foreach ($json['Title'] as $item) {
                if (isset($item['Language']) && isset($item['Value'])) {
                    // Decode the HTML entities and strip the tags.
                    $decodedTitle = html_entity_decode($item['Value'], ENT_QUOTES | ENT_HTML5, 'UTF-8');
                    $cleanedTitle = strip_tags(str_replace('<br>', ' ', $decodedTitle));
                    // Store the title in the array.
                    $title[$item['Language']] = $cleanedTitle;
                }
            }
        }

        // Extract the description from the JSON object.
        $description = [];
        if (isset($json['Description']) && is_array($json['Description'])) {
            foreach ($json['Description'] as $item) {
                if (isset($item['Language']) && isset($item['Value'])) {
                    // Decode the HTML entities and strip the tags.
                    $decodedDescription = html_entity_decode($item['Value'], ENT_QUOTES | ENT_HTML5, 'UTF-8');
                    $cleanedDescription = strip_tags(str_replace('<br>', ' ', $decodedDescription));
                    // Store the description in the array.
                    $description[$item['Language']] = $cleanedDescription;
                }
            }
        }

        // Extract the reference from the JSON object.
        $reference = null;
        if (isset($json['Reference'])) {
            $reference = $json['Reference'];
        }

        // Extract the image from the JSON object.
        $image = null;
        if (isset($json['Immagini']) && is_array($json['Immagini']) && count($json['Immagini']) > 0) {
            $image = $json['Immagini'][0];
        }
        
        // Check if the module already exists.
        $moduleExists = Module::where('code', $row['name'])->exists();
        $module = Module::where('code', $row['name'])->first();

        if ($moduleExists) {
            // Update the existing module
            $module->update([
                'code' => $row['name'],
                'name' => $row['name'],
                'reference' => $reference,
                'title' => $title,
                'description' => $description,
                'material' => $row['material'],
                'type' => $row['type'],
                'units' => $row['units'],
                'image' => $image ? 'images/modules/' . $image : null,
            ]);
        }
        else {
            return new Module([
                'code' => $row['name'],
                'name' => $row['name'],
                'reference' => $reference,
                'title' => $title,
                'description' => $description,
                'material' => $row['material'],
                'type' => $row['type'],
                'units' => $row['units'],
                'image' => $image ? 'images/modules/' . $image : null,
            ]);
        }
    }

    public function chunkSize(): int
    {
        return 10;
    }

    public function batchSize(): int
    {
        return 10;
    }

    /**
     * Retrive the JSON object with the given name.
     * 
     * @param string $targetName
     */
    private function findObjectByName(string $targetName): ?array
    {
        // Check if the file exists.
        if (!Storage::disk('public')->exists('import/modules.json')) {
            Log::error('File modules.json not found.');
            return null;
        }

        // Check if the target name is null.
        if ($targetName === null) {
            Log::error('Target name is null.');
            return null;
        }

        $filePath = Storage::disk('public')->path('import/modules.json');

        $foundObject = null;

        foreach (new JsonParser($filePath) as $key => $value) {
            if (isset($value['Name']) && $value['Name'] === $targetName) {
            $foundObject = $value;
            break;
            }
        }

        if ($foundObject) {
            Log::info('Found object: ' . json_encode($foundObject));
            return $foundObject;
        } else {
            Log::info('Object with Name ' . $targetName . ' not found.');
            return null;
        }
    }
}
