<?php

namespace App\Imports;

use App\Models\Module;
use App\Models\Assortment;
use Cerbero\JsonParser\JsonParser;
use Illuminate\Support\Collection;
use App\Models\PreconfiguredDrawer;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Concerns\Importable;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithProgressBar;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;

class AssortmentsImport implements ToCollection, WithChunkReading, WithBatchInserts, WithProgressBar, WithHeadingRow
{
    use Importable;

    public function collection(Collection $rows)
    {
        foreach ($rows as $row) {

            if (!isset($row['name'])) {
                return null;
            }
    
            // Retrive the JSON object with the given name.
            $json = $this->findObjectByName($row['name']);
    
            // Extract the title from the JSON object.
            $title = [];
            if (isset($json['Title']) && is_array($json['Title'])) {
                foreach ($json['Title'] as $item) {
                    if (isset($item['Language']) && isset($item['Value'])) {
                        // Decode the HTML entities and strip the tags.
                        $decodedTitle = html_entity_decode($item['Value'], ENT_QUOTES | ENT_HTML5, 'UTF-8');
                        $cleanedTitle = strip_tags(str_replace('<br>', ' ', $decodedTitle));
                        // Store the title in the array.
                        $title[$item['Language']] = $cleanedTitle;
                    }
                }
            }
    
            // Extract the description from the JSON object.
            $description = [];
            if (isset($json['Description']) && is_array($json['Description'])) {
                foreach ($json['Description'] as $item) {
                    if (isset($item['Language']) && isset($item['Value'])) {
                        // Decode the HTML entities and strip the tags.
                        $decodedDescription = html_entity_decode($item['Value'], ENT_QUOTES | ENT_HTML5, 'UTF-8');
                        $cleanedDescription = strip_tags(str_replace('<br>', ' ', $decodedDescription));
                        // Store the description in the array.
                        $description[$item['Language']] = $cleanedDescription;
                    }
                }
            }
    
            // Extract the reference from the JSON object.
            $reference = null;
            if (isset($json['Reference'])) {
                $reference = $json['Reference'];
            }
    
            // Extract the image from the JSON object.
            $image = null;
            if (isset($json['Immagini']) && is_array($json['Immagini']) && count($json['Immagini']) > 0) {
                $image = $json['Immagini'][0];
            }

            // Check if the assortment already exists.
            $assortmentExists = Assortment::where('code', $row['name'])->exists();
            $assortment = Assortment::where('code', $row['name'])->first();

            if ($assortmentExists) {
                // Update the existing assortment
                    $assortment->update([
                        'code' => $row['name'],
                        'name' => $row['name'],
                        'reference' => $reference,
                        'title' => $title,
                        'description' => $description,
                        'image' => $image ? 'images/assortments/' . $image : null,
                    ]);

                // Check if the preconfigured drawer exists.
                $drawerExists = PreconfiguredDrawer::query()
                    ->where('assortment_id', $assortment->id)
                    ->where('position', $row['drawer_position'])->exists();
                $drawer = PreconfiguredDrawer::query()
                    ->where('assortment_id', $assortment->id)
                    ->where('position', $row['drawer_position'])->first();
                $module = Module::query()->where('name', $row['module_name'])->first();

                if ($drawerExists) {
                    $drawer->update([
                        'format' => $module->format ?? NULL,
                    ]);

                    // Check if the module is already attached to the preconfigured drawer
                    if (!$drawer->modules()->where('module_id', $module->id)->exists()) {
                        // Create a new pivot record only if it doesn't exist
                        $drawer->modules()->attach($module->id, ['position' => $row['module_position']]);
                    }
                }
                else {
                    $drawer = PreconfiguredDrawer::create([
                        'position' => $row['drawer_position'],
                        'format' => $module->format ?? NULL,
                        'assortment_id' => $assortment->id,
                    ]);
                    $drawer->modules()->attach($module, ['position' => $row['module_position']]);
                }
            }
            else {
                $assortment = Assortment::create([
                    'code' => $row['name'],
                    'name' => $row['name'],
                    'reference' => $reference,
                    'title' => $title,
                    'description' => $description,
                    'image' => $image ? 'images/assortments/' . $image : null,
                ]);

                $module = Module::query()->where('name', $row['module_name'])->first();

                $drawer = PreconfiguredDrawer::create([
                    'position' => $row['drawer_position'],
                    'format' => $module->format ?? NULL,
                    'assortment_id' => $assortment->id,
                ]);

                $drawer->modules()->attach($module, ['position' => $row['module_position']]);
            }
        }
    }

    public function chunkSize(): int
    {
        return 10;
    }

    public function batchSize(): int
    {
        return 10;
    }

    /**
     * Retrive the JSON object with the given name.
     * 
     * @param string $targetName
     */
    private function findObjectByName(string $targetName): ?array
    {
        // Check if the file exists.
        if (!Storage::disk('public')->exists('import/modules.json')) {
            Log::error('File modules.json not found.');
            return null;
        }

        // Check if the target name is null.
        if ($targetName === null) {
            Log::error('Target name is null.');
            return null;
        }

        $filePath = Storage::disk('public')->path('import/modules.json');

        $foundObject = null;

        foreach (new JsonParser($filePath) as $key => $value) {
            if (isset($value['Name']) && $value['Name'] === $targetName) {
            $foundObject = $value;
            break;
            }
        }

        if ($foundObject) {
            Log::info('Found object: ' . json_encode($foundObject));
            return $foundObject;
        } else {
            Log::info('Object with Name ' . $targetName . ' not found.');
            return null;
        }
    }
}
