<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\HasMany;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\BelongsToMany;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class Configuration extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Configuration>
     */
    public static $model = \App\Models\Configuration::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'uuid',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make(__('UUID'), 'uuid')
                ->sortable()
                ->readonly(),

            Select::make(__('Module Material'), 'modules_material')
                ->options([
                    'rigid' => __('Rigid'),
                    'soft' => __('Soft'),
                ])
                ->displayUsingLabels()
                ->sortable()
                ->readonly(),

            BelongsTo::make(__('Product'), 'product', Product::class),

            BelongsTo::make(__('Assortment'), 'assortment', Assortment::class),

            BelongsTo::make(__('Variant'), 'variant', Variant::class),

            HasMany::make(__('Drawers'), 'drawers', Drawer::class),

            BelongsToMany::make(__('Accessories'), 'accessories', Accessory::class),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
