<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Image;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\KeyValue;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class Variant extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Variant>
     */
    public static $model = \App\Models\Variant::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'code';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'code', 'name',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make(__('Code'), 'code')
                ->sortable()
                ->rules('required', 'max:255')
                ->creationRules('unique:variants,code')
                ->updateRules('unique:variants,code,{{resourceId}}'),

            Text::make(__('Name'), 'name')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make(__('Reference'), 'reference')
                ->sortable()
                ->rules('max:255')
                ->nullable(),

            KeyValue::make(__('Title'), 'title')
                ->rules('json'),

            KeyValue::make(__('Description'), 'description')
                ->rules('json'),

            Image::make(__('Image'), 'image')
                ->disk('public')
                ->path('products')
                ->sortable()
                ->rules('image', 'mimes:jpeg,png,jpg,gif,svg', 'max:1024'),

            Select::make(__('Swatch'), 'swatch')
                ->options([
                    'orange' => 'Orange',
                    'orange-grey' => 'Orange - Grey',
                    'red' => 'Red',
                    'red-grey' => 'Red - Grey',
                    'blue' => 'Blue',
                    'blue-grey' => 'Blue - Grey',
                    'black' => 'Black',
                    'light-grey' => 'Light Grey',
                    'grey' => 'Grey',
                    'lightgrey-grey' => 'Light Grey - Grey',
                ])
                ->sortable()
                ->rules('required'),

            Text::make(__('Ral Code'), 'ral_code')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make(__('Color HEX'), 'color_hex')
                ->sortable()
                ->rules('required', 'max:255'),

            BelongsTo::make(__('Product'), 'product', Product::class)
                ->sortable()
                ->rules('required'),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
