<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\BelongsToMany;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class PreconfiguredDrawer extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\PreconfiguredDrawer>
     */
    public static $model = \App\Models\PreconfiguredDrawer::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Number::make(__('Position'), 'position')
                ->sortable(),

            Select::make(__('Orientation'), 'orientation')
                ->options([
                    'horizontal' => 'Horizontal',
                    'vertical' => 'Vertical',
                ])
                ->sortable(),

            Number::make(__('Format'), 'format')
                ->sortable(),

            BelongsTo::make(__('Assortment'), 'assortment', Assortment::class),

            BelongsToMany::make(__('Modules'), 'modules', Module::class)
                ->fields(function ($request, $relatedModel) {
                    return [
                        Number::make(__('Position'), 'position')
                            ->sortable(),
                    ];
                }),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
