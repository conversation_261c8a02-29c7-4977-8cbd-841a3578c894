<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Image;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON><PERSON>\Nova\Fields\KeyValue;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Http\Requests\NovaRequest;

class Module extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Module>
     */
    public static $model = \App\Models\Module::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'code', 'name', 'type', 'material',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make(__('Code'), 'code')
                ->sortable()
                ->rules('required', 'max:255')
                ->creationRules('unique:modules,code')
                ->updateRules('unique:modules,code,{{resourceId}}'),

            Text::make(__('Name'), 'name')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make(__('Reference'), 'reference')
                ->sortable()
                ->rules('max:255')
                ->nullable(),

            KeyValue::make(__('Title'), 'title')
                ->rules('json'),

            KeyValue::make(__('Description'), 'description')
                ->rules('json'),

            Select::make(__('Type'), 'type')
                ->options([
                    'std4' => __('STD4'),
                    'std3' => __('STD3'),
                    'mm1' => __('MM1'),
                ])
                ->displayUsingLabels()
                ->rules('required')
                ->sortable(),

            Select::make(__('Material'), 'material')
                ->options([
                    'rigid' => __('Rigid'),
                    'soft' => __('Soft'),
                ])
                ->displayUsingLabels()
                ->rules('required')
                ->sortable(),

            Number::make(__('Units'), 'units')
                ->sortable()
                ->rules('required', 'integer', 'min:1'),

            Image::make(__('Image'), 'image')
                ->disk('public')
                ->path('modules')
                ->sortable()
                ->rules('image', 'mimes:jpeg,png,jpg,gif,svg', 'max:1024'),

            BelongsToMany::make(__('Products'), 'products', Product::class),

            BelongsToMany::make(__('Components'), 'components', Component::class),

            BelongsToMany::make(__('Preconfigured Drawers'), 'preconfiguredDrawers', PreconfiguredDrawer::class),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
