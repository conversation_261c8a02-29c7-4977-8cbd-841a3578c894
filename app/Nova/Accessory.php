<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Image;
use <PERSON><PERSON>\Nova\Fields\KeyValue;
use <PERSON><PERSON>\Nova\Fields\Textarea;
use <PERSON><PERSON>\Nova\Fields\BelongsToMany;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class Accessory extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Accessory>
     */
    public static $model = \App\Models\Accessory::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'code', 'name',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Text::make(__('Code'), 'code')
                ->sortable()
                ->rules('required', 'max:255')
                ->creationRules('unique:accessories,code')
                ->updateRules('unique:accessories,code,{{resourceId}}'),

            Text::make(__('Name'), 'name')
                ->sortable()
                ->rules('required', 'max:255'),

            Text::make(__('Reference'), 'reference')
                ->sortable()
                ->rules('max:255')
                ->nullable(),

            Text::make(__('Title Label'), 'title_label')
                ->onlyOnIndex()
                ->readOnly(),

            KeyValue::make(__('Title'), 'title')
                ->rules('json'),

            KeyValue::make(__('Description'), 'description')
                ->rules('json'),

            Image::make(__('Image'), 'image')
                ->disk('public')
                ->path('accessories')
                ->sortable()
                ->rules('image', 'mimes:jpeg,png,jpg,gif,svg', 'max:1024'),

            BelongsToMany::make(__('Products'), 'products', Product::class),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
