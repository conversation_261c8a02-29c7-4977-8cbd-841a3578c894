<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\BelongsTo;
use <PERSON>vel\Nova\Fields\BelongsToMany;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

class Drawer extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Drawer>
     */
    public static $model = \App\Models\Drawer::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'id';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'uuid',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            ID::make()->sortable(),

            Boolean::make(__('Locked'), 'locked')
                ->sortable()
                ->readonly(),

            Number::make(__('Position'), 'position')
                ->sortable()
                ->readonly(),

            Text::make(__('Orientation'), 'orientation')
                ->sortable()
                ->readonly(),

            Number::make(__('Units'), 'units')
                ->sortable()
                ->readonly(),

            BelongsTo::make(__('Configuration'), 'configuration', Configuration::class),

            BelongsToMany::make(__('Modules'), 'modules', Module::class),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Laravel\Nova\Http\Requests\NovaRequest  $request
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
