<?php

namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Events\AfterSheet;
use Maatwebsite\Excel\Concerns\WithEvents;

class ConfigExport implements FromView, WithEvents
{
    protected $config;

    public function __construct($config)
    {
        $this->config = $config;
    }

    public function view(): View
    {
        return view('exports.configuration.excel', [
            'config' => $this->config,
        ]);
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                foreach (range('A', 'Z') as $column) { // Regola l'intervallo di colonne
                    $event->sheet->getDelegate()->getColumnDimension($column)->setAutoSize(true);
                }
            },
        ];
    }
}
