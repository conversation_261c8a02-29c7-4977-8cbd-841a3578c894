<?php

namespace App\Console\Commands;

use App\Imports\ProductsImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class SyncProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync products from Excel and JSON files.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting products sync...');
        (new ProductsImport)->withOutput($this->output)->import(Storage::disk('public')->path('import/products.xlsx'));
        $this->output->success('Products synced successfully!');
    }
}
