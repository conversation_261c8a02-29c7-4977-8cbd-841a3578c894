<?php

namespace App\Console\Commands;

use App\Imports\ModulesImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class SyncModules extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-modules';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync modules from Excel and JSON files.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting modules sync...');
        (new ModulesImport)->withOutput($this->output)->import(Storage::disk('public')->path('import/modules.xlsx'));
        $this->output->success('Modules synced successfully!');
    }
}
