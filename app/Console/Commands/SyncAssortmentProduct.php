<?php

namespace App\Console\Commands;

use App\Imports\AssortmentProductImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class SyncAssortmentProduct extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-assortment-product';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync assortment product pivot table from Excel files.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting assortments/products relation sync...');
        (new AssortmentProductImport)->withOutput($this->output)->import(Storage::disk('public')->path('import/assortment-product.xlsx'));
        $this->output->success('Relationships synced successfully!');
    }
}
