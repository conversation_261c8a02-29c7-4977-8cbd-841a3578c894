<?php

namespace App\Console\Commands;

use App\Imports\AccessoriesImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class SyncAccessories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-accessories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync accessories from Excel and JSON files.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting accessories sync...');
        (new AccessoriesImport)->withOutput($this->output)->import(Storage::disk('public')->path('import/accessories.xlsx'));
        $this->output->success('Accessories synced successfully!');
    }
}
