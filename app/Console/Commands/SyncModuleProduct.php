<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use App\Imports\ModuleProductImport;

class SyncModuleProduct extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-module-product';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync module product pivot table from Excel files.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting modules/products relation sync...');
        (new ModuleProductImport)->withOutput($this->output)->import(Storage::disk('public')->path('import/module-product.xlsx'));
        $this->output->success('Relationships synced successfully!');
    }
}
