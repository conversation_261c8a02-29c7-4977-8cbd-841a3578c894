<?php

namespace App\Console\Commands;

use App\Models\Module;
use App\Models\Component;
use Illuminate\Console\Command;
use Cerbero\JsonParser\JsonParser;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class SyncComponents extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-components';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync components from the JSON file.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting components sync...');

        // Get all the modules from the database.
        $modules = Module::all();

        // Make the bar progress.
        $this->output->progressStart($modules->count());

        foreach ($modules as $module) {

            // Make the bar move forward.
            $this->output->progressAdvance();
            
            // Get the module and it's components from the JSON file.
            $json = $this->findObjectByName($module->name);

            if ($json) {
                // Update or create the components in the database for the module.
                foreach ($json['Components'] as $componentData) {
                    
                    // Extract the image from the JSON object.
                    $image = null;
                    if (isset($componentData['Image'])) {
                        $image = $componentData['Image'];
                    }
                    // Extract the description from the JSON object.
                    $description = [];
                    if (isset($componentData['Description']) && is_array($componentData['Description'])) {
                        foreach ($componentData['Description'] as $item) {
                            if (isset($item['Language']) && isset($item['Value'])) {
                                // Decode the HTML entities and strip the tags.
                                $decodedDescription = html_entity_decode($item['Value'], ENT_QUOTES | ENT_HTML5, 'UTF-8');
                                $cleanedDescription = strip_tags(str_replace('<br>', ' ', $decodedDescription));
                                // Store the description in the array.
                                $description[$item['Language']] = $cleanedDescription;
                            }
                        }
                    }

                    // Check if the component exists by Image field
                    $componentCode = $module->code . '__' . pathinfo($image, PATHINFO_FILENAME);
                    $componentExists = Component::where('code', $componentCode)->exists();
                    $component = Component::where('code', $componentCode)->first();

                    if ($componentExists) {
                        // Update the existing component
                        $component->update([
                            'image' => $image ? 'images/components/' . $image : null,
                            // 'code' => $module->code . '__' . pathinfo($image, PATHINFO_FILENAME),
                            // 'name' => $module->code . '__' . pathinfo($image, PATHINFO_FILENAME),
                            'pieces' => (int) $componentData['Pieces'],
                            'description' => $description
                        ]);
                    } else {
                        // Create a new component
                        $component = Component::create([
                            'image' => $image ? 'images/components/' . $image : null,
                            'code' => $module->code . '__' . pathinfo($image, PATHINFO_FILENAME),
                            'name' => $module->code . '__' . pathinfo($image, PATHINFO_FILENAME),
                            'pieces' => (int) $componentData['Pieces'],
                            'description' => $description
                        ]);

                        // Attach the component to the module
                        $module->components()->attach($component->id);
                    }
                }
            }
        }

        // Finish the progress bar.
        $this->output->progressFinish();
        $this->output->success('Components synced successfully!');
    }

    /**
     * Retrive the JSON object with the given name.
     * 
     * @param string $targetName
     */
    private function findObjectByName(string $targetName): ?array
    {
        // Check if the file exists.
        if (!Storage::disk('public')->exists('import/modules.json')) {
            Log::error('File modules.json not found.');
            return null;
        }

        // Check if the target name is null.
        if ($targetName === null) {
            Log::error('Target name is null.');
            return null;
        }

        $filePath = Storage::disk('public')->path('import/modules.json');

        $foundObject = null;

        foreach (new JsonParser($filePath) as $key => $value) {
            if (isset($value['Name']) && $value['Name'] === $targetName) {
            $foundObject = $value;
            break;
            }
        }

        if ($foundObject) {
            Log::info('Found object: ' . json_encode($foundObject));
            return $foundObject;
        } else {
            Log::info('Object with Name ' . $targetName . ' not found.');
            return null;
        }
    }
}
