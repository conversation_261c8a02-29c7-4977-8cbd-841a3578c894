<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Imports\AssortmentsImport;
use Illuminate\Support\Facades\Storage;

class SyncAssortments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-assortments';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync assortments from Excel and JSON files.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting assortments sync...');
        (new AssortmentsImport)->withOutput($this->output)->import(Storage::disk('public')->path('import/assortments.xlsx'));
        $this->output->success('Assortments synced successfully!');
    }
}
