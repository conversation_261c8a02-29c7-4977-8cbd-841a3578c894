<?php

namespace App\Console\Commands;

use App\Imports\VariantsImport;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class SyncVariants extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-variants';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync variants from Excel files.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->output->title('Starting variants sync...');
        (new VariantsImport)->withOutput($this->output)->import(Storage::disk('public')->path('import/variants.xlsx'));
        $this->output->success('Variants synced successfully!');
    }
}
