<?php

namespace App\Providers;

use App\Nova\User;
use App\Nova\Drawer;
use App\Nova\Module;
use App\Nova\Product;
use App\Nova\Variant;
use App\Nova\Category;
use Laravel\Nova\Nova;
use App\Nova\Accessory;
use App\Nova\Component;
use App\Nova\Assortment;
use App\Nova\Configuration;
use Laravel\Nova\Menu\Menu;
use Illuminate\Http\Request;
use App\Nova\Dashboards\Main;
use Laravel\Nova\Menu\MenuItem;
use App\Nova\PreconfiguredDrawer;
use Laravel\Nova\Menu\MenuSection;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Blade;
use Laravel\Nova\NovaApplicationServiceProvider;

class NovaServiceProvider extends NovaApplicationServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        Nova::mainMenu(function (Request $request) {
            return [
                MenuSection::dashboard(Main::class)->icon('chart-bar'),

                MenuSection::make('Configurations', [
                    MenuItem::resource(Configuration::class),
                    MenuItem::resource(Drawer::class),
                ])->icon('clipboard-list')->collapsable(),

                MenuSection::make('Contents', [
                    MenuItem::resource(Category::class),
                    MenuItem::resource(Product::class),
                    MenuItem::resource(Variant::class),
                    MenuItem::resource(Module::class),
                    MenuItem::resource(Component::class),
                    MenuItem::resource(Assortment::class),
                    MenuItem::resource(PreconfiguredDrawer::class),
                    MenuItem::resource(Accessory::class),
                ])->icon('document-duplicate')->collapsable(),

                MenuSection::make('Settings', [
                    MenuItem::resource(User::class),
                ])->icon('cog')->collapsable(),

                Nova::footer(function ($request) {
                    return Blade::render('
                        <center>
                        Powered by <a href="https://mynd.it" target="_blank">Mynd</a>
                        </center>
                    ');
                }),
            ];
        });
    }

    /**
     * Register the Nova routes.
     *
     * @return void
     */
    protected function routes()
    {
        Nova::routes()
                ->withAuthenticationRoutes(default: true)
                ->withPasswordResetRoutes()
                ->register();
    }

    /**
     * Register the Nova gate.
     *
     * This gate determines who can access Nova in non-local environments.
     *
     * @return void
     */
    protected function gate()
    {
        Gate::define('viewNova', function ($user) {

            foreach (['beta-tools.com', 'mynd.it', 'example.com'] as $domain) {
                if (str_ends_with($user->email, $domain)) {
                    return true;
                }
            }
            return false;
        });
    }

    /**
     * Get the dashboards that should be listed in the Nova sidebar.
     *
     * @return array
     */
    protected function dashboards()
    {
        return [
            new \App\Nova\Dashboards\Main,
        ];
    }

    /**
     * Get the tools that should be listed in the Nova sidebar.
     *
     * @return array
     */
    public function tools()
    {
        return [];
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }
}
