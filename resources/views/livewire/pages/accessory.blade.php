<div class="grid flex-1 grid-cols-1 bg-neutral-01 lg:h-[calc(100vh_-_79px)] lg:grid-cols-[1fr_640px] mt-[127px] md:mt-[80px]"
    id="main-content">
    <div class="top-[127px] md:top-[80px] mb-[73px] self-start lg:sticky">
        <div class="flex flex-col">
            <div class="border-b border-sand-02 bg-white px-4 py-3 lg:pl-10 lg:pr-1">
                <p class="font-semibold text-sm md:text-base">
                    {{ $config->product->name }} - {{ $config->product->getTitleLabelAttribute($lang) }}
                </p>
            </div>
            <div class="mt-20 flex flex-1 items-start justify-center">
                <img src="{{ Storage::url($config->variant->image) }}" alt="" class="object-contain" width="440"
                    height="535" loading="lazy" decoding="async">
            </div>
        </div>
    </div>

    <div class="relative border-t border-sand-02 bg-white lg:border-l lg:border-t-0">

        <div class="h-full overflow-y-auto p-4 md:p-6 lg:p-10">

            <div class="mb-4 flex flex-wrap items-center justify-between gap-2">
                <p class="font-semibold text-base md:text-md">
                    {{ __('Would you like to add accessories?') }}
                </p>

                <div class="inline-flex h-6 appearance-none items-center rounded-full bg-orange-02 px-2 py-1">
                    <span class="text-xs">
                        {{ count($selectedAccessories) }} {{ __('accessories added') }}
                    </span>
                </div>
            </div>

            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                @foreach ($accessories as $accessory)
                    <label for="{{ $accessory->id }}"
                        class="block border border-sand-02 bg-white p-3 ring-1 ring-inset ring-transparent transition-all">
                        <div class="flex justify-between mb-2">
                            <div class="relative z-10">
                                <input wire:model.live='selectedAccessories' value="{{ $accessory->id }}"
                                    id="{{ $accessory->id }}" type="checkbox"
                                    class="absolute inset-0 inline-block cursor-pointer appearance-none rounded-sm border border-orange-04 bg-white transition-all checked:bg-orange-04 hover:ring-3 hover:ring-orange-02 [&:checked+*]:opacity-100 size-6">
                                <div
                                    class="pointer-events-none absolute inset-0 grid place-items-center text-white opacity-0 transition-opacity">
                                    <x-icons.check-line />
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col gap-3">
                            <div class="relative grid aspect-video h-full max-h-[130px] place-items-center w-full">
                                <img src="{{ Storage::url($accessory->image) }}" alt="" inferSize="true"
                                    class="absolute inset-0 size-full object-contain" width="3103" height="2539"
                                    loading="lazy" decoding="async">
                            </div>
                        </div>
                        <hr class="my-4 text-sand-02">
                        <div class="flex flex-col gap-4">
                            <div class="flex items-center justify-between">
                                <div class="text-orange-04">
                                    <p class="uppercase font-semibold text-sm">
                                        {{ $accessory->name }}
                                    </p>
                                </div>
                                <button data-card-button wire:click="showAccessoryDetails({{ $accessory->id }})"
                                    class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed rounded-full border border-transparent text-orange-04 hover:border-white hover:bg-orange-02 active:border-transparent active:bg-transparent active:text-orange-05 disabled:text-neutral-03 min-h-6 text-xs font-semibold">
                                    <span>{{ __('Details') }}</span>
                                    <x-icons.arrow-right-s-line />
                            </div>
                            <p class="text-sm">
                                {{ $accessory->getTitleLabelAttribute($lang) }}
                            </p>
                        </div>
                    </label>
                @endforeach
            </div>
        </div>
    </div>
</div>
