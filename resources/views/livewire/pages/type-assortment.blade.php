<div class="grid flex-1 grid-cols-1 bg-neutral-01 lg:h-[calc(100vh_-_79px)] lg:grid-cols-[1fr_640px] mt-[127px] md:mt-[80px] mb-[73px]"
    id="main-content">
    <div class="top-[127px] md:top-[80px] self-start lg:sticky">
        <div class="flex flex-col">
            <div class="border-b border-sand-02 bg-white px-4 py-3 lg:pl-10 lg:pr-1">
                <p class="font-semibold text-sm md:text-base">
                    {{ $config->product->name }} - {{ $config->product->getTitleLabelAttribute($lang) }}
                </p>
            </div>
            <div class="mt-20 flex flex-1 items-start justify-center">
                <img src="{{ Storage::url($config->variant->image) }}" alt="" class="object-contain" width="440"
                    height="535" loading="lazy" decoding="async">
            </div>
        </div>
    </div>

    <div x-data="{ modulesMoreInfoOpen: true }" class="relative border-t border-sand-02 bg-white lg:border-l lg:border-t-0">
        <div class="h-full overflow-y-auto p-4 md:p-6 lg:p-10">
            <div class="mb-4 flex items-center justify-between">
                <p class="font-semibold text-base md:text-md">{{ __('Choose the module material type') }}</p>
                <button x-on:click="modulesMoreInfoOpen = !modulesMoreInfoOpen" data-collapse-button
                    class="relative inline-grid size-8 place-items-center transition-colors rounded-full text-orange-04 hover:bg-orange-02 active:bg-transparent active:text-orange-05 disabled:text-neutral-03">
                    <x-icons.information-line />
                </button>
            </div>

            <div data-tab>
                {{-- Rigid or Soft Button --}}
                <div class="flex gap-4 *:flex-1" data-tab-header>
                    <button wire:click="setModulesType('rigid')" x-on:click="modulesMoreInfoOpen = false"
                        data-tab-trigger="tab-moduli-rigidi"
                        :class="$wire.selectedModulesType == 'rigid' ? 'border-orange-04 bg-orange-02 ring-1 ring-orange-04' :
                            ''"
                        class="min-h-20 w-full border border-sand-02 px-4 py-2 ring-inset transition-all hover:border-orange-03 hover:bg-orange-01 active:border-orange-04 active:bg-orange-02 active:ring-1 active:ring-orange-04">
                        <p class="text-sm">
                            {{ __('Rigid modules') }}
                        </p>
                    </button>
                    <button wire:click="setModulesType('soft')" x-on:click="modulesMoreInfoOpen = false"
                        data-tab-trigger="tab-moduli-morbidi"
                        :class="$wire.selectedModulesType == 'soft' ? 'border-orange-04 bg-orange-02 ring-1 ring-orange-04' : ''"
                        class="min-h-20 w-full border border-sand-02 px-4 py-2 ring-inset transition-all hover:border-orange-03 hover:bg-orange-01 active:border-orange-04 active:bg-orange-02 active:ring-1 active:ring-orange-04">
                        <p class="text-sm">
                            {{ __('Soft modules') }}
                        </p>
                    </button>
                </div>

                {{-- Modules More Info --}}
                <div x-show="modulesMoreInfoOpen" x-transition class="overflow-hidden" data-collapse-body>
                    <div data-collapse-container>
                        <div class="pt-4">
                            <div class="px-4 py-6 bg-neutral-01">

                                <div class="flex flex-col gap-6">
                                    <div class="flex flex-col gap-6 md:flex-row">
                                        <div class="shrink-0 grow-0 basis-auto">
                                            <img src="/assets/moduli-rigidi.CooACxwk_1opAen.webp" alt=""
                                                width="162" height="66" loading="lazy" decoding="async">
                                        </div>
                                        <div>
                                            <div class="mb-2 text-orange-04">
                                                <p class="font-semibold text-md md:text-lg">
                                                    {{ __('Rigid modules') }}
                                                </p>
                                            </div>
                                            <p class="text-xs">
                                                {{ __('Rigid modules description.') }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-6 md:flex-row">
                                        <div class="shrink-0 grow-0 basis-auto">
                                            <img src="/assets/moduli-morbidi.BC8uqtnv_10b14c.webp" alt=""
                                                width="162" height="66" loading="lazy" decoding="async">
                                        </div>
                                        <div>
                                            <div class="mb-2 text-orange-04">
                                                <p class="font-semibold text-md md:text-lg">
                                                    {{ __('Soft modules') }}
                                                </p>
                                            </div>
                                            <p class="text-xs">
                                                {{ __('Soft modules description.') }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Choose Assortment or Manual --}}
                <div x-show="$wire.selectedModulesType !== ''" x-transition class="mt-16" data-tab-container>
                    <div class="animate-opacity transition-all" id="tab-moduli-rigidi" data-tab-content>
                        <div class="mb-4">
                            <p class="font-semibold text-base md:text-md">
                                {{ __('How do you want to configure your drawers?') }}
                            </p>
                        </div>
                        <div data-tab>
                            <div class="flex gap-4 *:flex-1" data-tab-header>
                                <button wire:click="setCreationType('assortment')"
                                    data-tab-trigger="tab-moduli-rigidi-assortimenti-predefiniti"
                                    :class="$wire.selectedCreationType === 'assortment' ? 'border-orange-04 bg-orange-02 ring-1 ring-orange-04' : ''"
                                    class="min-h-20 w-full border border-sand-02 px-4 py-2 ring-inset transition-all hover:border-orange-03 hover:bg-orange-01 active:border-orange-04 active:bg-orange-02 active:ring-1 active:ring-orange-04">
                                    <p class="text-sm">
                                        {{ __('Configure predefined assortments') }}
                                    </p>
                                </button>
                                <button wire:click="setCreationType('manual')"
                                    data-tab-trigger="tab-manualmente"
                                    :class="$wire.selectedCreationType === 'manual' ? 'border-orange-04 bg-orange-02 ring-1 ring-orange-04' : ''"
                                    class="min-h-20 w-full border border-sand-02 px-4 py-2 ring-inset transition-all hover:border-orange-03 hover:bg-orange-01 active:border-orange-04 active:bg-orange-02 active:ring-1 active:ring-orange-04">
                                    <p class="text-sm">
                                        {{ __('Configure manually') }}
                                    </p>
                                </button>
                            </div>
                            <div data-tab-container class="mt-7">

                                {{-- Assortments Listing --}}
                                <div x-show="$wire.selectedCreationType == 'assortment'" x-transition class="animate-opacity transition-all"
                                    id="tab-moduli-rigidi-assortimenti-predefiniti" data-tab-content>
                                    <div class="bg-neutral-09 p-6">
                                        <div class="mb-4 text-white">
                                            <p class="font-semibold text-sm md:text-base">
                                                {{ __('Select the kit') }}
                                            </p>
                                        </div>
                                        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                                            @if ($assortments->isEmpty())
                                                <p class="text-white mt-4">{{ __('No results.') }}</p>
                                            @else
                                                @foreach ($assortments as $assortment)
                                                    @livewire(
                                                        'components.assortment-item',
                                                        [
                                                            'assortment' => $assortment,
                                                            'drawersCount' => $assortment->preconfiguredDrawers->count(),
                                                        ],
                                                        key($assortment->id)
                                                    )
                                                @endforeach
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                {{-- Message for manual select --}}
                                <div x-show="$wire.selectedCreationType == 'manual'" class="animate-opacity transition-all"
                                    id="tab-manualmente" data-tab-content>
                                    <div class="px-4 py-6 bg-neutral-01">
                                        <div class="flex items-center gap-4">
                                            <div class="flex-1">
                                                <x-icons.information-line />
                                            </div>
                                            <p class="text-sm">
                                                {{ __("You can add individual modules in the next step. Click 'Next' and configure the drawers.") }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
