<div class="grid flex-1 grid-cols-1 lg:h-[calc(100vh_-_79px)] lg:grid-cols-[1fr_640px] mt-[127px] md:mt-[80px] mb-[73px]"
    id="main-content">
    <div class="relative">
        <div class="flex flex-col gap-10 p-4 sm:pb-10 sm:pt-[50px] lg:px-10">
            <p class="font-semibold text-2xl md:text-4xl">
                {{ __('Your configuration') }}
            </p>
            <div>
                <div class="mb-4">
                    <p class="font-semibold text-xl md:text-3xl">
                        {{ __('Cabinet') }}
                    </p>
                    <p class="text-sm md:text-base">
                        {{ __('You choose to configure the drawers of the following model:') }}
                    </p>
                </div>
                <div class="flex flex-col flex-wrap md:items-center gap-4 bg-white md:p-3 sm:flex-row">
                    <div class="px-2 py-3">
                        <img src="{{ Storage::url($config->variant->image) }}" alt=""
                            class="aspect-square object-contain" width="120" height="120" loading="lazy"
                            decoding="async">
                    </div>
                    <div class="my-2 flex flex-col">
                        <div>
                            <p>
                                <span class="font-semibold text-md md:text-lg">{{ $config->product->name }}</span>
                                <span class="text-base md:text-md"> - Code: {{ $config->variant->reference }}</span>
                            </p>
                            <div class="mt-2">
                                <p class="text-base md:text-md">
                                    {{ $config->product->getTitleLabelAttribute($lang) }}
                                </p>
                            </div>
                        </div>
                        <div class="mt-auto">
                            <div class="inline-flex items-center justify-center gap-1">
                                <p class="text-base md:text-md">
                                    <b class="font-semibold">{{ __('Color:') }}</b>
                                    {{ $config->variant->ral_code }}
                                </p>
                                <x-dynamic-component component="colors.{{ $config->variant->swatch }}" />
                            </div>
                        </div>
                        <div class="mt-auto">
                            <div class="inline-flex items-center justify-center gap-1">
                                <p class="text-base md:text-md">
                                    <b class="font-semibold capitalize">{{ __('pieces') }}:</b>
                                    {{ $config->getTotalPiecesAttribute() }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Assortment --}}
            @if ($config->assortment)
                <div>
                    <div class="mb-4">
                        <p class="font-semibold text-xl md:text-3xl">
                            {{ __('Selected assortment') }}
                        </p>
                        <p class="text-sm md:text-base">
                            {{ __('Assortment material:') }}
                            {{ $config->modules_material == 'soft' ? __('Soft') : ($config->modules_material == 'rigid' ? __('Rigid') : '') }}
                        </p>
                        <p class="text-sm md:text-base">
                            {{ $config->assortment->getTitleLabelAttribute($lang) ?? '' }}
                        </p>
                    </div>
                    <div class="mb-2 bg-white py-4">
                        <div class="mb-2 text-orange-03">
                            <p class="font-semibold text-xs md:text-md">
                                {{ $config->assortment->name }}
                            </p>
                        </div>
                        <div class="mb-2 grid min-h-[142px] place-items-center bg-white">
                            <img src="{{ Storage::url($config->assortment->image) }}" class="w-[80%] object-contain"
                                loading="lazy" decoding="async">
                        </div>
                    </div>
                </div>

                {{-- Preconfigured Drawers --}}
                <div class="flex flex-col gap-6">
                    <div>
                        <p class="font-semibold text-xl md:text-3xl">
                            {{ __('Assortment drawers') }}
                        </p>
                        <p class="text-sm md:text-base">
                            {{ __('Modules material:') }}
                            {{ $config->modules_material == 'soft' ? __('Soft') : ($config->modules_material == 'rigid' ? __('Rigid') : '') }}
                        </p>
                    </div>

                    {{-- Drawers List --}}
                    @foreach ($config->drawers->where('locked', true) as $drawer)
                        <div x-data="{ open: false }" class="flex flex-col" data-accordion>
                            <div class="flex items-center justify-between gap-4 border-b py-2 border-orange-03">
                                <div class="text-neutral-08">
                                    <p class="font-semibold text-sm md:text-base">
                                        {{ __('Drawer') }} {{ $drawer->position }}
                                    </p>
                                </div>
                                <div class="inline-flex items-center gap-2 text-orange-04">

                                    @livewire(
                                        'components.drawer-status-bar',
                                        [
                                            'drawer' => $drawer,
                                            'isFull' => $drawer->isFull(),
                                            'usedUnits' => $drawer->getUsedUnits(),
                                            'availableUnits' => $drawer->getAvailableUnits(),
                                        ],
                                        key($drawer->id)
                                    )

                                    <button @click="open = !open"
                                        class="origin-center transition-transform duration-300" data-accordion-button>
                                        <div x-bind:class="{ 'rotate-180': open }"
                                            class="transition-transform duration-300">
                                            <x-icons.arrow-down-s-line />
                                        </div>
                                    </button>
                                </div>
                            </div>

                            <div x-show="open" x-transition data-accordion-body
                                class="overflow-hidden transition-all duration-300">
                                <div class="pt-3" data-accordion-container>
                                    <div class="grid grid-cols-2 gap-2 lg:grid-cols-3 xl:grid-cols-4">
                                        @forelse ($drawer->modules->sortBy('pivot.position') as $module)
                                            <div class="p-3 bg-white">
                                                <div class="flex justify-between mb-2 text-orange-04">
                                                    <p class="uppercase font-semibold text-xs">
                                                        {{ $module->name }}
                                                    </p>
                                                    @if ($module->pivot->position > $drawer->getTotalModulesFromAssortment())
                                                        <div class="ml-auto">
                                                            <p class="font-semibold text-xs">
                                                                {{ __('User added.') }}
                                                            </p>
                                                        </div>
                                                    @endif
                                                </div>

                                                <div
                                                    class="relative mb-2 w-full border border-sand-02 bg-white p-2 h-[70px]">
                                                    <img src="{{ Storage::url($module->image) }}"
                                                        class="absolute inset-0 size-full object-contain p-1"
                                                        inferSize="true" alt="" width="432" height="549"
                                                        loading="lazy" decoding="async">
                                                </div>

                                                <div class="mt-2 flex flex-col gap-2">
                                                    <p class="uppercase font-semibold text-xs">
                                                        {{ $module->name }} - Code: {{ $module->reference }}
                                                    </p>

                                                    <p class="text-xs">
                                                        {{ $module->getTitleLabelAttribute($lang) }}
                                                    </p>
                                                </div>
                                            </div>
                                        @empty
                                            <p class="text-sm md:text-base">
                                                {{ __('No modules added.') }}
                                            </p>
                                        @endforelse
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach

                </div>
            @endif

            {{-- Manually Configured Drawers --}}
            <div class="flex flex-col gap-6">
                <div>
                    <p class="font-semibold text-xl md:text-3xl">
                        {{ __('Personalized drawers') }}
                    </p>
                    <p class="text-sm md:text-base">
                        {{ __('Modules material:') }}
                        {{ $config->modules_material == 'soft' ? __('Soft') : ($config->modules_material == 'rigid' ? __('Rigid') : '') }}
                    </p>
                </div>

                {{-- Drawers List --}}
                @foreach ($config->drawers->where('locked', false) as $drawer)
                    <div x-data="{ open: false }" class="flex flex-col" data-accordion>
                        <div class="flex items-center justify-between gap-4 border-b py-2 border-orange-03">
                            <div class="text-neutral-08">
                                <p class="font-semibold text-sm md:text-base">
                                    {{ __('Drawer') }} {{ $drawer->position }}
                                </p>
                            </div>
                            <div class="inline-flex items-center gap-2 text-orange-04">

                                @livewire(
                                    'components.drawer-status-bar',
                                    [
                                        'drawer' => $drawer,
                                        'isFull' => $drawer->isFull(),
                                        'usedUnits' => $drawer->getUsedUnits(),
                                        'availableUnits' => $drawer->getAvailableUnits(),
                                    ],
                                    key($drawer->id)
                                )

                                <button @click="open = !open" class="origin-center transition-transform duration-300"
                                    data-accordion-button>
                                    <div x-bind:class="{ 'rotate-180': open }"
                                        class="transition-transform duration-300">
                                        <x-icons.arrow-down-s-line />
                                    </div>
                                </button>
                            </div>
                        </div>

                        <div x-show="open" x-transition data-accordion-body
                            class="overflow-hidden transition-all duration-300">
                            <div class="pt-3" data-accordion-container>
                                <div class="grid grid-cols-2 gap-2 lg:grid-cols-3 xl:grid-cols-4">
                                    @forelse ($drawer->modules->sortBy('pivot.position') as $module)
                                        <div class="p-3 bg-white">
                                            <div class="mb-2 text-orange-04">
                                                <p class="uppercase font-semibold text-xs">
                                                    {{ $module->name }}
                                                </p>
                                            </div>

                                            <div
                                                class="relative mb-2 w-full border border-sand-02 bg-white p-2 h-[70px]">
                                                <img src="{{ Storage::url($module->image) }}"
                                                    class="absolute inset-0 size-full object-contain p-1"
                                                    inferSize="true" alt="" width="432" height="549"
                                                    loading="lazy" decoding="async">
                                            </div>

                                            <div class="mt-2 flex flex-col gap-2">
                                                <p class="uppercase font-semibold text-xs">
                                                    {{ $module->name }} - Code: {{ $module->reference }}
                                                </p>

                                                <p class="text-xs">
                                                    {{ $module->getTitleLabelAttribute($lang) }}
                                                </p>
                                            </div>
                                        </div>
                                    @empty
                                        <p class="text-sm md:text-base">
                                            {{ __('No modules added.') }}
                                        </p>
                                    @endforelse
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            {{-- Accessories --}}
            @if ($config->accessories->isEmpty() === false)
                <div>
                    <div class="mb-4">
                        <p class="font-semibold text-xl md:text-3xl">
                            {{ __('Accessories') }}
                        </p>
                    </div>

                    <div class="grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3">
                        @foreach ($config->accessories as $accessory)
                            <div class="p-3 bg-white">
                                <div class="mb-2 text-orange-04">
                                </div>
                                <div class="relative mb-2 w-full border border-sand-02 bg-white p-2 h-[130px]">
                                    <img src="{{ Storage::url($accessory->image) }}"
                                        class="absolute inset-0 size-full object-contain p-1" inferSize="true"
                                        alt="" width="1365" height="1692" loading="lazy"
                                        decoding="async">
                                </div>
                                <div class="mt-2 flex flex-col gap-2">
                                    <p class="uppercase font-semibold text-xs">
                                        {{ $accessory->name }} - Code: {{ $accessory->reference ?? '' }}
                                    </p>
                                    <p class="text-xs">
                                        {{ $accessory->getDescriptionLabelAttribute($lang) }}
                                    </p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endif
        </div>

    </div>

    {{-- Sidebar - QuickSave | MyBeta | Download --}}
    <div class="top-12 self-start lg:sticky">
        <div class="px-6 py-12">
            <div class="flex flex-col gap-6">
                <div class="flex flex-col gap-4 bg-neutral-09 p-4 text-white sm:gap-8 sm:p-6 md:p-10">
                    <p class="font-semibold text-lg md:text-xl">
                        {{ __('Go to') }} <span class="text-orange-04">{{ __('My Beta') }}</span>
                        {{ __('and save the configuration') }}
                    </p>
                    <a wire:click="goToMyBeta"
                        class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed w-full disabeld:bg-neutral-03 bg-orange-04 text-white hover:bg-orange-03 active:bg-orange-05 disabled:bg-neutral-03 disabled:text-white min-h-10 cursor-pointer">
                        <span>{{ __('Login or register') }}</span>
                    </a>

                    <div class="grid grid-cols-[1fr_auto_1fr] items-center gap-4">
                        <div class="border-b border-b-white w-full"></div>
                        <p class="text-md md:text-lg">{{ __('or') }}</p>
                        <div class="border-b border-b-white w-full"></div>
                    </div>

                    <div class="grid sm:grid-cols-2 gap-4">
                        <button wire:click="downloadPDF" wire:target="downloadPDF" wire:loading.class="opacity-50"
                            wire:loading.attr="disabled"
                            class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed border border-orange-04 text-orange-04 hover:border-orange-03 hover:text-orange-03 active:border-orange-05 active:text-orange-05 disabled:border-neutral-03 disabled:text-neutral-03 min-h-10 cursor-pointer">
                            <span wire:loading.remove wire:target="downloadPDF"><x-icons.download-line
                                    :width="24" :height="24" /></span>
                            <span wire:loading.remove wire:target="downloadPDF">{{ __('Download PDF') }}</span>
                            <span wire:loading wire:target="downloadPDF">{{ __('Creating PDF..') }}</span>
                        </button>

                        <button wire:click="downloadExcel" wire:target="downloadExcel"
                            wire:loading.class="opacity-50" wire:loading.attr="disabled"
                            class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed border border-orange-04 text-orange-04 hover:border-orange-03 hover:text-orange-03 active:border-orange-05 active:text-orange-05 disabled:border-neutral-03 disabled:text-neutral-03 min-h-10 cursor-pointer">
                            <span wire:loading.remove wire:target="downloadExcel"><x-icons.download-line
                                    :width="24" :height="24" /></span>
                            <span wire:loading.remove wire:target="downloadExcel">{{ __('Download Excel') }}</span>
                            <span wire:loading wire:target="downloadExcel">{{ __('Creating XLSX..') }}</span>
                        </button>
                    </div>
                </div>

                <form wire:submit="quickCheckout" class="bg-sand-02 p-4 md:p-6">
                    <div class="mb-6 flex flex-col items-start gap-4 sm:flex-row sm:items-center">
                        <div class="bg-[#DED4C2] text-orange-04 leading-none p-3">
                            <x-icons.mail-send-line :width="40" :height="40" />
                        </div>
                        <div>
                            <p class="uppercase font-semibold text-sm">
                                {{ __('Are you in a rush?') }}
                            </p>
                            <p class="text-sm md:text-base">
                                {{ __('Fill the form with your email to receive the configuration in your inbox.') }}
                            </p>
                        </div>
                    </div>

                    <div class="flex flex-col gap-1 sm:flex-row sm:items-center">
                        <input wire:model.live="email" type="email" name="email" id="email"
                            class="palceholder:text-neutral-06 min-h-8 flex-1 appearance-none py-1 pl-3 pr-1 text-sm font-light text-neutral-10 focus:ring-1 focus:ring-orange-04 focus:outline-0 focus-visible:outline-none rounded-none"
                            placeholder="{{ __('Type your email..') }}">
                        <button type="submit"
                            class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed min-w-[80px] disabeld:bg-neutral-03 bg-orange-04 text-white hover:bg-orange-03 active:bg-orange-05 disabled:bg-neutral-03 disabled:text-white min-h-8 text-sm">
                            <span>{{ __('Send') }}</span>
                        </button>
                    </div>

                    <div class="mt-2 text-orange-04">
                        @error('email')
                            <span>{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="mt-4 flex items-center gap-2">
                        <div class="shrink-0">
                            <div class="relative size-4">
                                <input wire:model="acceptance" name="mail-form" id="mail-form" type="checkbox"
                                    class="absolute inset-0 inline-block cursor-pointer appearance-none border border-orange-04 bg-white transition-all checked:bg-orange-04 hover:ring-3 hover:ring-orange-02 [&:checked+*]:opacity-100 rounded-none">
                                <div
                                    class="pointer-events-none absolute inset-0 grid place-items-center text-white opacity-0 transition-opacity">
                                    <x-icons.check-line :width="16" :height="16" />
                                </div>
                            </div>
                        </div>
                        <label for="mail-form">
                            {{ __('I have read and accepted the ') }} <a class="underline"
                                href="#">{{ __('Privacy Policy') }}</a>
                        </label>
                    </div>
                    <div class="mt-2 text-orange-04">
                        @error('acceptance')
                            <span>{{ $message }}</span>
                        @enderror
                    </div>
                    @if (session()->has('success'))
                        <div class="bg-green text-white px-4 py-2 mt-4">
                            {{ session('success') }}
                        </div>
                    @endif
                    @if (session()->has('error'))
                        <div class="bg-yellow text-black px-4 py-2 mt-4">
                            {{ session('error') }}
                        </div>
                    @endif
                </form>

                <div class="flex flex-col items-start gap-4 bg-sand-02 p-4 sm:flex-row sm:items-center md:p-6">
                    <div class="bg-[#DED4C2] text-orange-04 leading-none p-3">
                        <x-icons.user-smile-line :width="40" :height="40" />
                    </div>

                    <div>
                        <div class="sm:ml-2">
                            <p class="uppercase font-semibold text-sm">
                                {{ __('Do you need tailored advice?') }}
                            </p>
                        </div>

                        <div class="text-orange-04">
                            <a href="{{ __('Summary - Tailored advice LINK') }}" target="_blank"
                                class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed rounded-full border border-transparent text-orange-04 hover:border-white hover:bg-orange-02 active:border-transparent active:bg-transparent active:text-orange-05 disabled:text-neutral-03 min-h-10"><span>
                                    {{ __('Find your trusted Beta dealer') }}
                                </span>
                                <x-icons.arrow-right-s-line />
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
