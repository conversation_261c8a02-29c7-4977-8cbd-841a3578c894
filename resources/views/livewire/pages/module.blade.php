<div class="lg:grid flex-1 bg-neutral-01 grid-cols-[1fr_640px] mt-[127px] md:mt-[80px] mb-[73px]" id="main-content">
    <div class="top-[127px] md:top-[80px] self-start lg:sticky">
        <div class="flex flex-col">

            {{-- Product Name and Drawers --}}
            <div class="border-b border-sand-02 bg-white pt-3">
                <div class="px-4 lg:pl-10">
                    <p class="font-semibold text-sm md:text-base">
                        {{ $config->product->name }} - {{ $config->product->getTitleLabelAttribute($lang) }}
                    </p>
                </div>

                {{-- Drawers Listing --}}
                <div class="mt-3">
                    <div
                        class="flex overflow-x-auto scrollbar scrollbar-track-orange-03 scrollbar-thumb-neutral-05 scrollbar-h-px lg:w-full">
                        @foreach ($config->drawers as $drawer)
                            {{-- Single Drawer --}}
                            <button wire:click="selectDrawer({{ $drawer->id }})" wire:key="drawer-{{ $drawer->id }}"
                                class="relative inline-flex appearance-none flex-col items-center gap-2 text-nowrap px-3 py-2 transition-colors hover:bg-white">
                                @if ($drawer->isLocked())
                                    <span class="absolute -top-1 right-1 grid size-5 place-items-center text-orange-04">
                                        <x-icons.lock-fill class="text-orange-04" />
                                    </span>
                                @endif

                                @livewire(
                                    'components.drawer-status-bar',
                                    [
                                        'drawer' => $drawer,
                                        'isFull' => $drawer->isFull(),
                                        'usedUnits' => $drawer->getUsedUnits(),
                                        'availableUnits' => $drawer->getAvailableUnits(),
                                    ],
                                    key($drawer->id)
                                )

                                <p class="text-xs">
                                    {{ __('drawer') . ' ' . $drawer->position }}
                                </p>

                                <div :class="{ 'opacity-0': {{ $drawer->id }} !== {{ $selectedDrawer->id }} }"
                                    class="absolute inset-x-0 bottom-0 mx-auto h-1 w-11/12 rounded-full bg-orange-04 transition-opacity">
                                </div>
                            </button>

                            @if (!$loop->last)
                                <hr class="h-14 w-px border-l border-neutral-02">
                            @endif
                        @endforeach
                    </div>
                </div>
            </div>

            {{-- Drawer View --}}
            <div>
                <div class="px-4 pb-14 lg:pb-4">

                    {{-- Cabinet Top Render --}}
                    <div class="mx-auto max-w-[650px] relative z-30">
                        <img src="/assets/cab-top-render.webp" alt="" class="" width="650"
                            height="115" loading="lazy" decoding="async">
                    </div>

                    {{-- Cabinet Drawer and Modules - Default --}}
                    @if ($this->getDrawerOrientation() === '')
                        @if ($config->product->drawer_template === 'mm1' || $config->product->drawer_template === 'mm2')
                            @livewire(
                                'components.drawer-templates.' . $config->product->getDrawerTemplateForPosition($selectedDrawer->position) . '.mm',
                                [
                                    'modules' => $selectedDrawer->modules->sortBy('pivot.position'),
                                    'drawer' => $selectedDrawer,
                                ],
                                key('drawer-' . $selectedDrawer->id . '-' . now()->timestamp)
                            )
                        @else
                            @livewire(
                                'components.drawer-templates.' . $config->product->getDrawerTemplateForPosition($selectedDrawer->position) . '.vertical',
                                [
                                    'modules' => $selectedDrawer->modules->sortBy('pivot.position'),
                                    'drawer' => $selectedDrawer,
                                ],
                                key('drawer-' . $selectedDrawer->id . '-' . now()->timestamp)
                            )
                        @endif

                        {{-- Cabinet Drawer and Modules - Vertical --}}
                    @elseif ($this->getDrawerOrientation() === 'vertical')
                        @livewire(
                            'components.drawer-templates.' . $config->product->getDrawerTemplateForPosition($selectedDrawer->position) . '.vertical',
                            [
                                'modules' => $selectedDrawer->modules->sortBy('pivot.position'),
                                'drawer' => $selectedDrawer,
                            ],
                            key('drawer-' . $selectedDrawer->id . '-' . now()->timestamp)
                        )

                        {{-- Cabinet Drawer and Modules - Horizontal --}}
                    @elseif ($this->getDrawerOrientation() === 'horizontal')
                        @livewire(
                            'components.drawer-templates.' . $config->product->getDrawerTemplateForPosition($selectedDrawer->position) . '.horizontal',
                            [
                                'modules' => $selectedDrawer->modules->sortBy('pivot.position'),
                                'drawer' => $selectedDrawer,
                            ],
                            key('drawer-' . $selectedDrawer->id . '-' . now()->timestamp)
                        )

                        {{-- Cabinet Drawer and Modules - MM --}}
                    @elseif ($this->getDrawerOrientation() === 'mm')
                        @livewire(
                            'components.drawer-templates.' . $config->product->getDrawerTemplateForPosition($selectedDrawer->position) . '.mm',
                            [
                                'modules' => $selectedDrawer->modules->sortBy('pivot.position'),
                                'drawer' => $selectedDrawer,
                            ],
                            key('drawer-' . $selectedDrawer->id . '-' . now()->timestamp)
                        )

                        {{-- Cabinet Drawer and Modules - Standard Modules in MM Drawer --}}
                    @elseif ($this->getDrawerOrientation() === 'standard')
                        @livewire(
                            'components.drawer-templates.' . $config->product->getDrawerTemplateForPosition($selectedDrawer->position) . '.standard',
                            [
                                'modules' => $selectedDrawer->modules->where('pivot.position', '>', 0), // Greater than 0 to exclude the filler modules
                                'drawer' => $selectedDrawer,
                                'material' => $config->modules_material,
                            ],
                            key('drawer-' . $selectedDrawer->id . '-' . now()->timestamp)
                        )
                    @endif
                </div>
            </div>
        </div>
    </div>

    {{-- Modules - Sidebar --}}
    <div class="relative overflow-hidden border-l border-sand-02 bg-white">

        <div class="h-full overflow-y-auto p-4 md:p-6 lg:p-10">

            {{-- Modules Listing - Title --}}
            <p class="font-semibold text-base md:text-md">
                {{ __('What would you like to insert in the drawers?') }}
            </p>

            {{-- Modules Listing - Search --}}
            <div class="mt-4">
                <div class="relative flex w-full items-center gap-2 rounded-full bg-sand-01 p-3">

                    <x-icons.search-line />
                    <input wire:model.live="search" placeholder="{{ __('Search..') }}" type="text"
                        class="w-full appearance-none bg-transparent text-sm text-neutral-10 placeholder:text-neutral-06 focus:outline-0 focus-visible:outline-none [&:not(:placeholder-shown)+*]:opacity-100">
                    <button wire:click="resetSearch" class="text-neutral-10 opacity-0 transition-opacity">
                        <div class="ri-xl leading-none">
                            <x-icons.close-line />
                        </div>
                    </button>
                </div>
            </div>

            {{-- Module Filters --}}
            <div class="my-10">
                <div class="flex flex-wrap gap-2">
                    <button wire:click="resetFilters" data-module
                        class="relative inline-flex min-w-[84px] items-center justify-center gap-2 rounded-full border px-2 py-0.5 text-center transition-colors border-orange-04 bg-orange-04 text-white">
                        <span class="font-semibold text-xs">{{ __('All') }}</span>
                    </button>

                    @foreach ($availableFilters as $availableFilter)
                        <div class="relative inline-block">
                            <input wire:model.live="filters" type="radio" value="{{ $availableFilter['code'] }}"
                                type="radio"
                                class="absolute inset-0 cursor-pointer appearance-none rounded-full opacity-0 [&:checked+*]:outline-orange-04 [&:checked+*]:outline-1 [&:checked+*]:border-orange-04 size-full">
                            <div
                                class="outline outline-transparent inline-flex items-center gap-2 rounded-full border border-neutral-03 px-3 py-1 text-neutral-08 transition-all">
                                <span class="text-sm">
                                    {{ $availableFilter['name'] }}
                                </span>

                                @if ($availableFilter['orientation'] === 'vertical')
                                    <div data-module-line
                                        class="inline-flex gap-0.5 *:rounded-[1px] *:h-4 *:w-1.5 *:flex-1 [&#38;>*:nth-child(-n_+_2)]:bg-orange-03">
                                        @for ($i = 0; $i < $availableFilter['format'] - ($availableFilter['format'] - $availableFilter['units']); $i++)
                                            <div class="bg-orange-04"></div>
                                        @endfor
                                        @for ($i = 0; $i < $availableFilter['format'] - $availableFilter['units']; $i++)
                                            <div class="bg-neutral-02"></div>
                                        @endfor
                                    </div>
                                @elseif ($availableFilter['orientation'] === 'horizontal')
                                    <div data-module-line
                                        class="inline-flex gap-0.5 *:rounded-[1px] flex-col *:h-1 *:w-[30px] [&#38;>*:nth-child(1)]:bg-orange-03">
                                        @for ($i = 0; $i < $availableFilter['format'] - ($availableFilter['format'] - $availableFilter['units']); $i++)
                                            <div class="bg-orange-04"></div>
                                        @endfor
                                        @for ($i = 0; $i < $availableFilter['format'] - $availableFilter['units']; $i++)
                                            <div class="bg-neutral-02"></div>
                                        @endfor
                                    </div>
                                @elseif ($availableFilter['orientation'] === 'mm')
                                    <div data-module-line
                                        class="inline-flex *:rounded-[1px] *:h-4 *:w-1.5 *:flex-1 [&#38;>*:nth-child(-n_+_2)]:bg-orange-03">
                                        <div class="bg-orange-04"></div>
                                        <div class="bg-orange-04"></div>
                                        <div class="bg-orange-04"></div>
                                        <div class="bg-orange-04"></div>
                                    </div>
                                @elseif ($availableFilter['orientation'] === 'standard')
                                    <div data-module-line
                                        class="inline-flex gap-0.5 *:rounded-[1px] *:h-4 *:w-1.5 *:flex-1 [&#38;>*:nth-child(-n_+_2)]:bg-orange-03">
                                        @for ($i = 0; $i < $availableFilter['format'] - ($availableFilter['format'] - $availableFilter['units']); $i++)
                                            <div class="bg-orange-04"></div>
                                        @endfor
                                        @for ($i = 0; $i < $availableFilter['format'] - $availableFilter['units']; $i++)
                                            <div class="bg-neutral-02"></div>
                                        @endfor
                                    </div>
                                @else
                                    <div data-module-line
                                        class="inline-flex *:rounded-[1px] *:h-4 *:w-1.5 *:flex-1 [&#38;>*:nth-child(-n_+_2)]:bg-orange-03">
                                        <div class="bg-orange-04"></div>
                                        <div class="bg-orange-04"></div>
                                        <div class="bg-orange-04"></div>
                                        <div class="bg-orange-04"></div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            {{-- Modules Listing --}}
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                @if ($modules->isEmpty())
                    @if ($selectedDrawer->isFull())
                        <p class="mt-4">{{ __('The drawer is full.') }}</p>
                    @elseif ($selectedDrawer->isLocked())
                        <p class="mt-4">{{ __('The drawer is locked.') }}</p>
                    @else
                        <p class="mt-4">{{ __('No results.') }}</p>
                    @endif
                @else
                    @foreach ($modules as $module)
                        {{-- Module Card --}}
                        <div class="border border-sand-02 bg-white p-3 flex flex-col">
                            <div class="mb-3 flex items-center justify-between">
                                <div
                                    class="relative inline-flex min-w-[84px] items-center justify-center gap-2 rounded-full border px-2 py-0.5 text-center transition-colors border-neutral-03 bg-white">
                                    <div class="inline-flex items-center gap-2">
                                        <span class="font-semibold text-xs">
                                            {{ $module->units . '/' . $module->format }}
                                        </span>

                                        @if ($module->pivot->orientation === 'vertical')
                                            <div data-module-line
                                                class="inline-flex gap-0.5 *:rounded-[1px] *:h-4 *:w-1.5 *:flex-1 [&#38;>*:nth-child(-n_+_2)]:bg-orange-03">
                                                @for ($i = 0; $i < $module->format - ($module->format - $module->units); $i++)
                                                    <div class="bg-orange-04"></div>
                                                @endfor
                                                @for ($i = 0; $i < $module->format - $module->units; $i++)
                                                    <div class="bg-neutral-02"></div>
                                                @endfor
                                            </div>
                                        @elseif ($module->pivot->orientation === 'horizontal')
                                            <div data-module-line
                                                class="inline-flex gap-0.5 *:rounded-[1px] flex-col *:h-1 *:w-[30px] [&#38;>*:nth-child(1)]:bg-orange-03">
                                                @for ($i = 0; $i < $module->format - ($module->format - $module->units); $i++)
                                                    <div class="bg-orange-04"></div>
                                                @endfor
                                                @for ($i = 0; $i < $module->format - $module->units; $i++)
                                                    <div class="bg-neutral-02"></div>
                                                @endfor
                                            </div>
                                        @elseif ($module->pivot->orientation === 'mm')
                                            <div data-module-line
                                                class="inline-flex *:rounded-[1px] *:h-4 *:w-1.5 *:flex-1 [&#38;>*:nth-child(-n_+_2)]:bg-orange-03">
                                                <div class="bg-orange-04"></div>
                                                <div class="bg-orange-04"></div>
                                                <div class="bg-orange-04"></div>
                                                <div class="bg-orange-04"></div>
                                            </div>
                                        @elseif ($module->pivot->orientation === 'standard')
                                            <div data-module-line
                                                class="inline-flex gap-0.5 *:rounded-[1px] *:h-4 *:w-1.5 *:flex-1 [&#38;>*:nth-child(-n_+_2)]:bg-orange-03">
                                                @for ($i = 0; $i < $module->format - ($module->format - $module->units); $i++)
                                                    <div class="bg-orange-04"></div>
                                                @endfor
                                                @for ($i = 0; $i < $module->format - $module->units; $i++)
                                                    <div class="bg-neutral-02"></div>
                                                @endfor
                                            </div>
                                        @else
                                            <div data-module-line
                                                class="inline-flex *:rounded-[1px] *:h-4 *:w-1.5 *:flex-1 [&#38;>*:nth-child(-n_+_2)]:bg-orange-03">
                                                <div class="bg-orange-04"></div>
                                                <div class="bg-orange-04"></div>
                                                <div class="bg-orange-04"></div>
                                                <div class="bg-orange-04"></div>
                                            </div>
                                        @endif
                                    </div>
                                </div>

                                {{-- Already Inserted Pill --}}
                                <div class="ml-auto">
                                    <div class="relative inline-block">
                                        @if ($this->isAlreadyInserted($module->id))
                                            <button
                                                class="inline-flex h-6 appearance-none items-center gap-2 rounded-full bg-neutral-09 px-2 py-1 text-white hover:bg-neutral-08 [&:focus+*]:pointer-events-auto [&:focus+*]:translate-y-0 [&:focus+*]:opacity-100">
                                                <p class="font-semibold text-xs">
                                                    {{ __('Already inserted') }}
                                                </p>
                                                <x-icons.information-line :width="18" :height="18" />
                                            </button>
                                        @endif

                                        {{-- Already Inserted - Details Dropdown --}}
                                        {{-- <div class="pointer-events-none absolute right-0 z-10 mt-1 min-w-44 translate-y-1/4 rounded-lg bg-white px-2 py-1 opacity-0 shadow-sm shadow-black/25 transition-all">
                                            <div class="p-2">
                                                <p class="text-xs">
                                                    cassetto 1: <b class="font-semibold">2 moduli</b>
                                                </p>
                                            </div>
                                            <hr class="text-neutral-03">
                                            <div class="p-2">
                                                <p class="text-xs">
                                                    cassetto 3: <b class="font-semibold">1 moduli</b>
                                                </p>
                                            </div>
                                        </div> --}}

                                    </div>
                                </div>
                            </div>

                            <div class="flex flex-col gap-3">
                                <div class="grid aspect-video max-h-[230px] place-items-center px-2 w-full">
                                    <img src="{{ Storage::url($module->image) }}"
                                        class="object-contain h-full w-full max-h-[230px]" alt=""
                                        inferSize="true" width="228" height="114" loading="lazy"
                                        decoding="async">
                                </div>
                            </div>

                            <hr class="my-4 text-sand-02">

                            <div class="flex flex-col gap-4 flex-1">
                                <div class="flex items-center justify-between">
                                    <div class="text-orange-04">
                                        <p class="uppercase font-semibold text-sm">
                                            {{ $module->name }}
                                        </p>
                                    </div>

                                    <button data-card-button wire:click="showModuleDetails({{ $module->id }})"
                                        class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed rounded-full border border-transparent text-orange-04 hover:border-white hover:bg-orange-02 active:border-transparent active:bg-transparent active:text-orange-05 disabled:text-neutral-03 min-h-6 text-xs font-semibold"><span>
                                            {{ __('Details') }}
                                        </span>
                                        <x-icons.arrow-right-s-line />
                                    </button>
                                </div>

                                <p class="text-sm">
                                    {{ $module->getTitleLabelAttribute($lang) }}
                                </p>

                                <button wire:click="addModule({{ $module->id }})"
                                    class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed w-full disabeld:bg-neutral-03 bg-orange-04 text-white hover:bg-orange-03 active:bg-orange-05 disabled:bg-neutral-03 disabled:text-white min-h-8 text-sm mt-auto">
                                    <span>{{ __('Add module') }}</span>
                                </button>
                            </div>
                        </div>
                    @endforeach
                @endif
            </div>
        </div>
    </div>
</div>
