<div
    class="bg-white flex justify-between gap-2 lg:gap-4 items-center py-4 px-2 lg:px-10 border-t border-neutral-02 shadow-[0px_-2px_10px_0px_rgba(235,_230,_220,_0.4)] fixed bottom-0 w-full z-50">
    {{-- Summary Modal --}}
    <button wire:click="previousStep" @if ($step == 1 || $step == 2 || $step == 3) disabled @endif
        class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed sm:min-w-[120px] border border-orange-04 text-orange-04 hover:bg-white/20 active:bg-white/10 disabled:border-neutral-03 disabled:text-neutral-03 min-h-10">
        <span>{{ __('Back') }}</span>
    </button>

    <div class="flex items-center justify-end gap-2 max-md:ms-auto">
        {{-- Summary Modal --}}
        <button wire:click="showSummaryDetails()" @if ($step == 1 || $step == 2) disabled @endif
            class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed sm:min-w-[120px] border border-orange-04 text-orange-04 hover:bg-white/20 active:bg-white/10 disabled:border-neutral-03 disabled:text-neutral-03 min-h-10">
            <span>{{ __('Summary') }}</span>
        </button>

        {{-- Next Button --}}
        @if ($step != 5)
            <button wire:click="nextStep" :disabled="$wire.isNextButtonEnabled === false"
                class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed sm:min-w-[120px] disabeld:bg-neutral-06 bg-orange-04 text-white hover:bg-orange-03 active:bg-orange-05 disabled:bg-neutral-06 disabled:text-neutral-03 min-h-10">
                <span>{{ __('Next Step') }}</span>
            </button>
        @endif
    </div>
</div>
