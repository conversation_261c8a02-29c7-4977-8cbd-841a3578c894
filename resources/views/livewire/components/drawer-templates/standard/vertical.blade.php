<div id="draggable" class="relative mx-auto flex items-center justify-center max-w-[600px]">
    {{-- Drawer Image --}}
    <div>
        <img src="/assets/drawers/standard.webp" alt="" inferSize="true" class="size-full object-contain"
            width="1638" height="1051" loading="lazy" decoding="async">
    </div>
    <div
        class="absolute border-[3px] border-dashed border-yellow bg-white/30 text-center text-white left-1/2 top-2 size-[89%] max-h-[344px] max-w-[546px] -translate-x-1/2 lg:size-full">
        {{-- Drawer Grid --}}
        <div class="absolute inset-0 grid size-full grid-cols-4 grid-rows-3">
            <div class="col-span-1 row-span-3 border-r-[3px] border-dashed border-yellow"></div>
            <div class="col-span-1 row-span-3 border-r-[3px] border-dashed border-yellow"></div>
            <div class="col-span-1 row-span-3 border-r-[3px] border-dashed border-yellow"></div>
        </div>
        {{-- Inserted Modules --}}
        <div x-sort="$wire.updatePosition($item, $position)"
            class="absolute inset-0 grid gap-0.5 grid-cols-4 grid-rows-3">
            @foreach ($modules as $module)
                <div x-sort:item="{{ $module->id }}" wire:key="module-{{ $module->id }}"
                    class="relative col-span-{{ $module->units }} row-span-4">
                    <img src="{{ Storage::url($module->image) }}" inferSize="true" alt=""
                        class="absolute inset-0 size-full rounded-[4px] object-cover" width="422" height="530"
                        loading="lazy" decoding="async">

                    @if (!$drawer->configuration->assortment?->preconfiguredDrawers->where('position', $drawer->position)->first()?->modules?->pluck('id')->contains($module->id) || 
                        $drawer->modules->where('id', $module->id)->count() > $drawer->configuration->assortment?->preconfiguredDrawers->where('position', $drawer->position)->first()?->modules?->where('id', $module->id)->count())
                        <button wire:click="clearModule({{ $module->pivot?->id }})"
                            class="inline-flex h-6 appearance-none items-center rounded-full bg-orange-04 px-2 py-1 text-white hover:bg-orange-03 active:bg-orange-05 [&:focus+*]:pointer-events-auto [&:focus+*]:translate-y-0 [&:focus+*]:opacity-100 absolute -translate-x-1/2 -bottom-16">
                            <x-icons.close-line :width="18" :height="18" />
                        </button>
                    @endif
                </div>
            @endforeach
        </div>
    </div>
</div>
