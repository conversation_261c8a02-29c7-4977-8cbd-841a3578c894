<div id="draggable" class="relative mx-auto flex max-w-[320px] items-center justify-center lg:max-w-[400px]">
    <div class="-translate-y-[20px]">
        <img src="/assets/drawers/deep.webp" alt="" inferSize="true" class="size-full object-contain" width="1582"
            height="2454" loading="lazy" decoding="async" />
    </div>

    <div
        class="absolute top-[80px] h-[270px] w-[430px] rotate-90 border-[3px] border-dashed border-yellow bg-white/30 text-center text-white lg:top-[110px] lg:h-[354px] lg:w-[566px]">
        <div class="absolute inset-0 grid size-full grid-cols-4 grid-rows-3">
            <div class="col-span-1 row-span-3 border-r-[3px] border-dashed border-yellow"></div>
            <div class="col-span-1 row-span-3 border-r-[3px] border-dashed border-yellow"></div>
            <div class="col-span-1 row-span-3 border-r-[3px] border-dashed border-yellow"></div>
        </div>

        <div x-sort="$wire.updatePosition($item, $position)"
            class="absolute inset-0 grid grid-cols-4 grid-rows-3 gap-0.5">
            @foreach ($modules as $module)
                <div x-sort:item="{{ $module->id }}" wire:key="module-{{ $module->id }}"
                    class="relative col-span-{{ $module->units }} row-span-4">
                    <img src="{{ Storage::url($module->image) }}" inferSize="true" alt=""
                        class="absolute inset-0 size-full rounded-[4px] object-fill" width="422" height="530"
                        loading="lazy" decoding="async" />

                    @if (!$drawer->configuration->assortment?->preconfiguredDrawers->where('position', $drawer->position)->first()?->modules?->pluck('id')->contains($module->id) || 
                        $drawer->modules->where('id', $module->id)->count() > $drawer->configuration->assortment?->preconfiguredDrawers->where('position', $drawer->position)->first()?->modules?->where('id', $module->id)->count())
                        <button wire:click="clearModule({{ $module->pivot?->id }})"
                            class="inline-flex h-6 appearance-none items-center rounded-full bg-orange-04 px-2 py-1 text-white hover:bg-orange-03 active:bg-orange-05 [&:focus+*]:pointer-events-auto [&:focus+*]:translate-y-0 [&:focus+*]:opacity-100 absolute -translate-x-1/2 -bottom-16">
                            <x-icons.close-line :width="18" :height="18" />
                        </button>
                    @endif
                </div>
            @endforeach
        </div>
    </div>
</div>
