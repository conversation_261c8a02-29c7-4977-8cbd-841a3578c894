<div id="draggable" class="relative mx-auto flex items-center justify-center max-w-[600px]">
    <div>
        <img src="/assets/drawers/mm2.webp" alt="" inferSize="true" class="size-full object-contain" width="1114"
            height="813" loading="lazy" decoding="async">
    </div>
    <div
        class="absolute border-[3px] border-dashed border-yellow bg-white/30 text-center text-white left-1/2 top-2 size-[92%] h-[94%] max-h-[404px] max-w-[567px] -translate-x-1/2">
        <div class="absolute inset-0 grid grid-cols-[1fr_1fr_1fr_0.8fr] grid-rows-[0.5fr_1fr_1fr_1fr] size-full">
            <div class="border-b-[3px] border-dashed border-yellow col-span-3 row-span-1"></div>
            <div class="border-r-[3px] border-dashed border-yellow col-span-1 row-span-3"></div>
            <div class="border-r-[3px] border-dashed border-yellow col-span-1 row-span-3"></div>
            <div class="border-r-[3px] border-dashed border-yellow col-span-1 row-span-3"></div>
        </div>
        <div x-sort="$wire.updatePosition($item, $position)" class="absolute inset-0 grid gap-0.5 grid-cols-[1fr_1fr_1fr_0.8fr] grid-rows-[0.5fr_1fr_1fr_1fr]">
            <div class="relative col-start-1 -col-end-2">
                @if ($material == 'rigid')
                    <img src="/assets/mm1-minuteria-rigid-1.png" inferSize="true" alt=""
                        class="absolute inset-0 size-full rounded-[4px] object-fill" width="3451" height="474"
                        loading="lazy" decoding="async">
                @elseif ($material == 'soft')
                    <img src="/assets/mm1-minuteria-soft-1.webp" inferSize="true" alt=""
                        class="absolute inset-0 size-full rounded-[4px] object-fill" width="3451" height="474"
                        loading="lazy" decoding="async">
                @endif
            </div>
            <div class="relative -col-start-1 -col-end-2 row-start-1 -row-end-1">
                @if ($material == 'rigid')
                    <img src="/assets/mm1-minuteria-rigid-2.png" inferSize="true" alt=""
                        class="absolute inset-0 size-full rounded-[4px] object-fill" width="3451" height="474"
                        loading="lazy" decoding="async">
                @elseif ($material == 'soft')
                    <img src="/assets/mm1-minuteria-soft-2.webp" inferSize="true" alt=""
                        class="absolute inset-0 size-full rounded-[4px] object-fill" width="1326" height="4291"
                        loading="lazy" decoding="async">
                @endif
            </div>
            @foreach ($modules as $module)
                <div x-sort:item="{{ $module->id }}" wire:key="module-{{ $module->id }}" class="relative col-span-{{ $module->units }} row-span-4">
                    <img src="{{ Storage::url($module->image) }}" inferSize="true" alt=""
                        class="absolute inset-0 size-full rounded-[4px] object-fill" width="422" height="530"
                        loading="lazy" decoding="async">

                    @if (!$drawer->configuration->assortment?->preconfiguredDrawers->where('position', $drawer->position)->first()?->modules?->pluck('id')->contains($module->id) || 
                        $drawer->modules->where('id', $module->id)->count() > $drawer->configuration->assortment?->preconfiguredDrawers->where('position', $drawer->position)->first()?->modules?->where('id', $module->id)->count())
                        <button wire:click="clearModule({{ $module->pivot?->id }})"
                            class="inline-flex h-6 appearance-none items-center rounded-full bg-orange-04 px-2 py-1 text-white hover:bg-orange-03 active:bg-orange-05 [&:focus+*]:pointer-events-auto [&:focus+*]:translate-y-0 [&:focus+*]:opacity-100 absolute -translate-x-1/2 -bottom-16">
                            <x-icons.close-line :width="18" :height="18" />
                        </button>
                    @endif
                </div>
            @endforeach
        </div>
    </div>
</div>
