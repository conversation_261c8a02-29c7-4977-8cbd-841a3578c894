<div
    class="group relative size-10 xl:size-5 [&:hover>:first-child]:pointer-events-auto [&:hover>:first-child]:translate-y-0 [&:hover>:first-child]:opacity-100">
    <div
        class="pointer-events-none absolute bottom-full left-1/2 z-10 mb-1 flex h-5 w-max -translate-x-1/2 translate-y-1/4 items-center justify-center rounded-lg bg-white px-2 py-1 text-center opacity-0 shadow-sm shadow-black/25 transition-all duration-300">
        <p class="text-xs">{{ $variant->ral_code }}</p>
    </div>

    <input wire:click.stop="selectVariant()" type="radio" name="variant" @if ($selectedProduct && $selectedVariant && $selectedProduct->is($product) && $selectedVariant->is($variant)) checked @endif
        class="absolute inset-0 cursor-pointer appearance-none transition-shadow checked:ring-2 checked:ring-green rounded-none size-full">

    <div
        class="inline-block transition-shadow duration-300 group-hover:ring-2 group-hover:ring-yellow *:size-10 xl:*:size-5">
        <x-dynamic-component component="colors.{{ $variant->swatch }}" />
    </div>
</div>
