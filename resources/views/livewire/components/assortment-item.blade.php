<div>
    {{-- Single Assortment Item --}}
    <label class="block border border-sand-02 bg-white p-3 ring-1 ring-inset ring-transparent transition-all h-full"
        for="assortment-{{ $assortment->id }}">
        <div class="flex justify-between mb-4">
            <div class="relative size-6">
                <input wire:click="selectAssortment()" type="radio" name="assortment"
                    id="assortment-{{ $assortment->id }}"
                    class="absolute inset-0 cursor-pointer appearance-none rounded-full border border-orange-04 bg-white transition-all hover:ring-3 hover:ring-orange-02 [&:checked+*]:opacity-100 size-full">
                <div class="pointer-events-none absolute inset-0 grid place-items-center opacity-0 transition-opacity">
                    <div class="rounded-full bg-orange-04 size-3"></div>
                </div>
            </div>
            <div class="ms-auto">
                <div class="inline-flex h-6 appearance-none items-center rounded-full bg-orange-02 px-2 py-1">
                    <span class="text-xs">
                        {{ __('drawers n.') }} {{ $drawersCount }}
                    </span>
                </div>
            </div>
        </div>
        <div class="flex flex-col gap-3">
            <div class="relative grid aspect-video h-full max-h-[130px] place-items-center w-full">
                <img src="{{ Storage::url($assortment->image) }}" alt="" inferSize="true"
                    class="absolute inset-0 size-full object-contain" width="2280" height="599" loading="lazy"
                    decoding="async">
            </div>
        </div>

        <hr class="my-4 text-sand-02">

        <div class="flex flex-col gap-4">
            <div class="flex items-center justify-between">
                <div class="text-orange-04">
                    <p class="uppercase font-semibold text-sm">
                        {{ $assortment->name }}
                    </p>
                </div>
                <button data-card-button wire:click="showAssortmentDetails({{ $assortment->id }})"
                    class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed rounded-full border border-transparent text-orange-04 hover:border-white hover:bg-orange-02 active:border-transparent active:bg-transparent active:text-orange-05 disabled:text-neutral-03 min-h-6 text-xs font-semibold"><span>
                        {{ __('Details') }}
                    </span>
                    <x-icons.arrow-right-s-line />
            </div>
            <p class="text-sm">
                {{ $assortment->getDescriptionLabelAttribute($lang) }}
            </p>
        </div>
    </label>
</div>
