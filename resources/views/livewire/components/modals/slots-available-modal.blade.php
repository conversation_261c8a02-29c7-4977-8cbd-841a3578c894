<div x-show="$wire.showModal" x-transition class="fixed inset-0 z-50 grid place-items-center overflow-auto bg-black/50"
    data-modal>
    <div class="mx-auto p-4 w-full max-w-[516px]">
        <div class="relative bg-white p-4 lg:p-10">
            <button wire:click="closeModal" class="absolute right-6 top-6">
                <x-icons.close-line />
            </button>

            <div class="mb-6">
                <div class="mb-4">
                    <p class="font-semibold text-md md:text-lg">
                        {{ __('There is still space!') }}
                    </p>
                </div>

                <p class="text-sm md:text-base">
                    {{ __('There are drawers that still have available space. Are you sure you want to continue without filling all the drawers?') }}
                </p>
            </div>

            <div class="grid grid-cols-1 gap-4 lg:grid-cols-2">
                @if ($config)
                    @foreach ($config->drawers as $drawer)
                        <div class="flex items-center justify-between flex-wrap rounded-[4px] bg-sand-01 p-3">
                            <div class="flex items-center gap-4">
                                @livewire(
                                    'components.drawer-status-bar',
                                    [
                                        'drawer' => $drawer,
                                        'isFull' => $drawer->isFull(),
                                        'usedUnits' => $drawer->getUsedUnits(),
                                        'availableUnits' => $drawer->getAvailableUnits(),
                                    ],
                                    key($drawer->id)
                                )

                                <div>
                                    <p class="text-base md:text-md font-semibold">
                                        {{ __('Drawer') }} {{ $drawer->position }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @endif
            </div>

            <div class="mt-10 flex justify-end">
                <a wire:click="confirmToContinue"
                    class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed disabeld:bg-neutral-03 bg-orange-04 text-white hover:bg-orange-03 active:bg-orange-05 disabled:bg-neutral-03 disabled:text-white min-h-10 cursor-pointer">
                    <span>{{ __('Continue without filling') }}</span>
                </a>
            </div>
        </div>
    </div>
</div>
