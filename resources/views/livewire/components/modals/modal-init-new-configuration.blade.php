<div x-show="$wire.showModal" x-transition class="fixed inset-0 z-50 grid place-items-center overflow-auto bg-black/50"
    data-modal>
    <div class="mx-auto p-4 max-w-[516px] w-full">
        <div class="relative bg-white p-4 lg:p-10">
            <button wire:click="closeModal" class="absolute right-6 top-6">
                <x-icons.close-line />
            </button>

            <div class="mb-6">
                <div class="mb-4">
                    <p class="font-semibold text-md md:text-lg">
                        Vuoi iniziare una nuova configurazione?
                    </p>
                </div>
            </div>

            <div class="mt-10 flex flex-col gap-4">
                <a wire:click="confirmToContinue"
                    class="inline-flex items-center justify-center gap-1 px-2 transition-colors border border-orange-04 text-orange-04 hover:border-orange-03 hover:text-orange-03 active:border-orange-05 active:text-orange-05 min-h-10 cursor-pointer">
                    <span>Annulla</span>
                </a>

                <a wire:click="confirmToContinue"
                    class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed disabeld:bg-neutral-03 bg-orange-04 text-white hover:bg-orange-03 active:bg-orange-05 disabled:bg-neutral-03 disabled:text-white min-h-10 cursor-pointer">
                    <span>Inizia nuova configurazione</span>
                </a>
            </div>
        </div>
    </div>
</div>
