<div>
    @if ($showModal)
        <div x-data="{ show: false }" x-show="show" x-init="$nextTick(() => show = true)" x-transition:enter-start="translate-y-full"
            x-transition:enter-end="-translate-y-0" x-transition:leave-start="-translate-y-0"
            x-transition:leave-end="translate-y-full"
            class="fixed inset-0 z-50 flex min-h-screen flex-col bg-neutral-09 text-white transition-transform duration-300"
            data-summary>
            <div class="grid flex-1 grid-cols-1 overflow-y-auto md:grid-cols-2">
                <div class="top-0 self-start p-4 md:p-6 lg:sticky lg:p-10">
                    <div class="mb-16">
                        <p class="font-semibold text-2xl md:text-4xl">
                            {{ __('Summary') }}
                        </p>
                        <div class="my-6">
                            <p class="font-semibold text-md md:text-lg">
                                {{ $config->product->name }} - {{ $config->product->getTitleLabelAttribute($lang) }}
                            </p>
                        </div>

                        <div
                            class="inline-flex items-center justify-center gap-1 h-9 rounded-full bg-white px-4 text-neutral-10">
                            <p class="text-base md:text-md">
                                <b class="font-semibold">{{ __('Color:') }}</b>
                                {{ $config->variant->ral_code }}
                            </p>
                            <x-dynamic-component component="colors.{{ $config->variant->swatch }}" />
                        </div>
                    </div>

                    <div class="mt-16 flex items-center justify-center">
                        <img src="{{ Storage::url($config->variant->image) }}" alt="" width="440"
                            height="535" loading="lazy" decoding="async">
                    </div>
                </div>

                <div class="bg-neutral-10 p-4 md:p-6 lg:p-10">
                    {{-- Close Modal --}}
                    <button wire:click="closeModal" class="fixed right-5 top-5 lg:right-10 lg:top-10"
                        data-close-summary>
                        <x-icons.close-line />
                    </button>

                    <div class="md:mt-10">
                        <div class="flex flex-col gap-16">

                            {{-- Assortment --}}
                            @if ($config->assortment)
                                <div>
                                    <div class="mb-4">
                                        <p class="font-semibold text-2lg md:text-2xl">
                                            {{ __('Selected assortment') }}
                                        </p>
                                        <p class="text-sm md:text-base">
                                            {{ __('Assortment material:') }}
                                            {{ $config->modules_material == 'soft' ? __('Soft') : ($config->modules_material == 'rigid' ? __('Rigid') : '') }}
                                        </p>
                                    </div>

                                    <div class="bg-neutral-09 p-4">
                                        <div class="mb-2 text-orange-03">
                                            <p class="font-semibold text-xs">
                                                {{ $config->assortment->name }}
                                            </p>
                                        </div>
                                        <div class="mb-2 grid min-h-[142px] place-items-center bg-white">
                                            <img src="{{ Storage::url($config->assortment->image) }}" alt=""
                                                width="256" height="142" loading="lazy" decoding="async">
                                        </div>

                                        {{-- Assortment Details --}}
                                        <div class="mb-4">
                                            <p class="font-semibold text-xs">
                                                {{ $config->assortment->name }}
                                            </p>
                                            <p class="text-xs">
                                                {{ $config->assortment->getTitleLabelAttribute($lang) }}
                                            </p>
                                        </div>

                                        {{-- Preconfigured Drawers --}}
                                        <div class="flex flex-col gap-6">
                                            @foreach ($config->drawers->where('locked', true) as $drawer)
                                                <div x-data="{ open: false }" class="flex flex-col" data-accordion>
                                                    <div
                                                        class="flex items-center justify-between gap-4 border-b py-2 border-white">
                                                        <div class="text-white">
                                                            <p class="font-semibold text-sm md:text-base">
                                                                {{ __('Drawer') }} {{ $drawer->position }}
                                                            </p>
                                                        </div>
                                                        <div class="inline-flex items-center gap-2 text-white">
                                                            @livewire(
                                                                'components.drawer-status-bar',
                                                                [
                                                                    'drawer' => $drawer,
                                                                    'isFull' => $drawer->isFull(),
                                                                    'usedUnits' => $drawer->getUsedUnits(),
                                                                    'availableUnits' => $drawer->getAvailableUnits(),
                                                                ],
                                                                key($drawer->id)
                                                            )

                                                            <button @click="open = !open"
                                                                class="origin-center transition-transform duration-300"
                                                                data-accordion-button>
                                                                <div x-bind:class="{ 'rotate-180': open }"
                                                                    class="transition-transform duration-300">
                                                                    <x-icons.arrow-down-s-line />
                                                                </div>
                                                            </button>
                                                        </div>
                                                    </div>

                                                    <div x-show="open" x-transition data-accordion-body
                                                        class="overflow-hidden transition-all duration-300">
                                                        <div class="pt-3" data-accordion-container>
                                                            <div
                                                                class="grid grid-cols-2 gap-2 lg:grid-cols-3 xl:grid-cols-4">
                                                                @forelse ($drawer->modules->sortBy('pivot.position') as $module)
                                                                    <div class="p-3 bg-neutral-09 text-white">
                                                                        <div class="flex justify-between mb-2 text-orange-03">
                                                                            <p class="uppercase font-semibold text-xs">
                                                                                 {{ $module->name }}
                                                                            </p>
                                                                            @if ($module->pivot->position > $drawer->getTotalModulesFromAssortment())
                                                                                <div class="ml-auto">
                                                                                    <p class="font-semibold text-[10px]">
                                                                                        {{ __('User added.') }}
                                                                                    </p>
                                                                                </div>
                                                                            @endif
                                                                        </div>

                                                                        <div
                                                                            class="relative mb-2 w-full border border-sand-02 bg-white p-2 h-[70px]">
                                                                            <img src="{{ Storage::url($module->image) }}"
                                                                                class="absolute inset-0 size-full object-contain p-1"
                                                                                inferSize="true" alt=""
                                                                                width="432" height="549"
                                                                                loading="lazy" decoding="async">
                                                                        </div>

                                                                        <div class="mt-2 flex flex-col gap-2">
                                                                            <p class="uppercase font-semibold text-xs">
                                                                                {{ $module->name }}
                                                                            </p>

                                                                            <p class="text-xs">
                                                                                {{ $module->getTitleLabelAttribute($lang) }}
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                @empty
                                                                    <p class="text-sm md:text-base">
                                                                        {{ __('No modules added.') }}
                                                                    </p>
                                                                @endforelse
                                                            </div>
                                                        </div>
                                                    </div>

                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endif

                            {{-- Manually Configured Drawers --}}
                            <div class="flex flex-col gap-6">
                                <div>
                                    <p class="font-semibold text-2lg md:text-2xl">
                                        {{ __('Personalized drawers') }}
                                    </p>
                                    <p class="text-sm md:text-base">
                                        {{ __('Modules material:') }}
                                        {{ $config->modules_material == 'soft' ? __('Soft') : ($config->modules_material == 'rigid' ? __('Rigid') : '') }}
                                    </p>
                                </div>

                                {{-- Drawers List --}}
                                @foreach ($config->drawers->where('locked', false) as $drawer)
                                    <div x-data="{ open: false }" class="flex flex-col" data-accordion>
                                        <div class="flex items-center justify-between gap-4 border-b py-2 border-white">
                                            <div class="text-white">
                                                <p class="font-semibold text-sm md:text-base">
                                                    {{ __('Drawer') }} {{ $drawer->position }}
                                                </p>
                                            </div>
                                            <div class="inline-flex items-center gap-2 text-white">
                                                @livewire(
                                                    'components.drawer-status-bar',
                                                    [
                                                        'drawer' => $drawer,
                                                        'isFull' => $drawer->isFull(),
                                                        'usedUnits' => $drawer->getUsedUnits(),
                                                        'availableUnits' => $drawer->getAvailableUnits(),
                                                    ],
                                                    key($drawer->id)
                                                )

                                                <button @click="open = !open"
                                                    class="origin-center transition-transform duration-300"
                                                    data-accordion-button>
                                                    <div x-bind:class="{ 'rotate-180': open }"
                                                        class="transition-transform duration-300">
                                                        <x-icons.arrow-down-s-line />
                                                    </div>
                                                </button>
                                            </div>
                                        </div>

                                        <div x-show="open" x-transition data-accordion-body
                                            class="overflow-hidden transition-all duration-300">
                                            <div class="pt-3" data-accordion-container>
                                                <div class="grid grid-cols-2 gap-2 lg:grid-cols-3 xl:grid-cols-4">
                                                    @forelse ($drawer->modules->sortBy('pivot.position') as $module)
                                                        <div class="p-3 bg-neutral-09 text-white">
                                                            <div class="mb-2 text-orange-03">
                                                                <p class="uppercase font-semibold text-xs">
                                                                     {{ $module->name }}
                                                                </p>
                                                            </div>

                                                            <div
                                                                class="relative mb-2 w-full border border-sand-02 bg-white p-2 h-[70px]">
                                                                <img src="{{ Storage::url($module->image) }}"
                                                                    class="absolute inset-0 size-full object-contain p-1"
                                                                    inferSize="true" alt="" width="432"
                                                                    height="549" loading="lazy" decoding="async">
                                                            </div>

                                                            <div class="mt-2 flex flex-col gap-2">
                                                                <p class="uppercase font-semibold text-xs">
                                                                    {{ $module->name }}
                                                                </p>

                                                                <p class="text-xs">
                                                                    {{ $module->getTitleLabelAttribute($lang) }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    @empty
                                                        <p class="text-sm md:text-base">
                                                            {{ __('No modules added.') }}
                                                        </p>
                                                    @endforelse
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            {{-- Accessories --}}
                            @if ($config->accessories->isEmpty() === false)
                                <div>
                                    <div class="mb-4">
                                        <p class="font-semibold text-2lg md:text-2xl">
                                            {{ __('Accessories') }}
                                        </p>
                                    </div>

                                    <div class="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-3">
                                        @foreach ($config->accessories as $accessory)
                                            <div class="p-3 bg-neutral-09 text-white">
                                                <div class="mb-2 text-orange-03">
                                                </div>
                                                <div
                                                    class="relative mb-2 w-full border border-sand-02 bg-white p-2 h-[130px]">
                                                    <img src="{{ Storage::url($accessory->image) }}"
                                                        class="absolute inset-0 size-full object-contain p-1"
                                                        inferSize="true" alt="" width="1365"
                                                        height="1692" loading="lazy" decoding="async">
                                                </div>
                                                <div class="mt-2 flex flex-col gap-2">
                                                    <p class="uppercase font-semibold text-xs">
                                                        {{ $accessory->name }}
                                                    </p>
                                                    <p class="text-xs">
                                                        {{ $accessory->getDescriptionLabelAttribute($lang) }}
                                                    </p>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
