<div>
    @if ($showModal)
        <div x-data="{ show: false }" x-show="show" x-init="$nextTick(() => show = true)" x-transition:enter-start="translate-x-full"
            x-transition:enter-end="translate-x-0" x-transition:leave-start="translate-x-0"
            x-transition:leave-end="translate-x-full"
            class="fixed inset-y-0 right-0 z-40 mt-[127px] md:mt-[80px] w-full max-w-screen-sm border-l border-sand-02 bg-white transition-transform duration-500"
            data-offcanvas>
            <div class="flex h-full flex-col">
                <div class="h-full overflow-y-auto p-4 md:p-6 lg:p-10">
                    <div class="flex-1 overflow-y-auto">
                        <div class="flex h-full flex-col">
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <button wire:click="closeModal"
                                        class="inline-flex items-center gap-1 text-md font-semibold" data-back-to-list>
                                        <x-icons.arrow-left-s-line />
                                        {{ $assortment->name }}
                                    </button>
                                </div>
                                <div class="my-4">
                                    <p class="text-sm">{{ $assortment->getTitleLabelAttribute($lang) }}</p>
                                </div>

                                <div class="my-6">
                                    <div class="flex flex-wrap items-center gap-2">
                                        {{-- <div class="inline-flex items-center gap-2">
                                            <x-icons.layout-2-line />
                                            <p class="font-semibold text-sm">
                                                17 Pieces
                                            </p>
                                        </div> --}}

                                        {{-- <hr class="h-5 border-l border-neutral-03"> --}}

                                        {{-- <div class="inline-flex items-center gap-2">
                                            <x-icons.weight-line />
                                            <p class="font-semibold text-sm">
                                                2,7 Kg
                                            </p>
                                        </div> --}}
                                    </div>
                                </div>

                                <div class="aspect-video bg-white overflow-hidden border border-sand-02">
                                    <img src="{{ Storage::url($assortment->image) }}" alt=""
                                        class="h-full object-contain" width="2280" height="1152" loading="lazy"
                                        decoding="async">
                                </div>

                                <div class="mt-10">
                                    <p class="font-semibold text-sm">
                                        {{ __('Modules') }}
                                    </p>
                                </div>

                                <div class="mt-4">
                                    <table class="w-full table-auto text-xs">
                                        <thead class="bg-sand-01">
                                            <tr class="whitespace-nowrap *:px-3 *:py-2 *:font-semibold">
                                                <th></th>
                                                <th>{{ __('Code') }}</th>
                                                <th class="text-left">{{ __('Description') }}</th>
                                                <th>{{ __('Pieces n.') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($modules as $module)
                                                <tr class="border-t border-sand-02 text-center *:h-16 *:px-3 *:py-2">
                                                    <td>
                                                        <img src="{{ Storage::url($module->image) }}" alt=""
                                                            width="66" height="48" loading="lazy"
                                                            decoding="async">
                                                    </td>
                                                    <td class="font-bold text-orange-04">{{ $module->name }}</td>
                                                    <td class="text-left">{{ $module->getTitleLabelAttribute($lang) }}</td>
                                                    <td>{{ $module->components->sum('pieces') }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="border-t border-sand-02 bg-white">
                    <div class="px-4 py-6 md:px-6 lg:px-10">
                        <div class="flex items-end justify-end">
                            <button wire:click="selectAssortment({{ $assortment->id }})" data-back-to-list
                                class="inline-flex items-center justify-center gap-1 px-2 transition-colors disabled:cursor-not-allowed disabeld:bg-neutral-03 bg-orange-04 text-white hover:bg-orange-03 active:bg-orange-05 disabled:bg-neutral-03 disabled:text-white min-h-10"><span>
                                    {{ __('Select assortment') }}
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
