<div class="inline-flex gap-[2px] *:rounded-sm *:h-4 *:w-2 *:flex-1">
    @if ($isFull)
        @for ($i = 0; $i < $drawer->units; $i++)
            <div class="bg-green"></div>
        @endfor
    @elseif ($usedUnits == 0 || !$usedUnits)
        @if (!$usedUnits)
            <div class="bg-sand-02"></div>
            <div class="bg-sand-02"></div>
            <div class="bg-sand-02"></div>
            <div class="bg-sand-02"></div>
        @else
            @for ($i = 0; $i < $drawer->units; $i++)
                <div class="bg-sand-02"></div>
            @endfor
        @endif
    @else
        @for ($i = 0; $i < $usedUnits; $i++)
            <div class="bg-orange-04"></div>
        @endfor
        @for ($i = 0; $i < $availableUnits; $i++)
            <div class="bg-sand-02"></div>
        @endfor
    @endif
</div>
