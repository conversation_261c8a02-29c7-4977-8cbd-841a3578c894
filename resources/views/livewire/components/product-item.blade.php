<a wire:click="selectProduct()" :class="$wire.isActive ? 'bg-orange-01 outline-2 !outline-orange-04' : 'bg-white'"
    class="p-4 transition-all duration-300 hover:bg-orange-01 outline outline-transparent cursor-pointer flex flex-col">
    <div class="flex flex-col gap-3">
        <div class="relative grid size-full min-h-[260px] place-items-center border border-sand-02 bg-white px-1.5">
            @if ($selectedVariant && $selectedProduct->id == $product->id)
                <img src="{{ Storage::url($selectedVariant->image) }}" alt="" inferSize="true"
                    class="absolute inset-0 size-full object-contain" width="600" height="600" loading="lazy"
                    decoding="async">
            @else
                <img src="{{ Storage::url($product->image) }}" alt="" inferSize="true"
                    class="absolute inset-0 size-full object-contain" width="600" height="600" loading="lazy"
                    decoding="async">
            @endif
        </div>
    </div>

    <div class="mt-4 flex flex-col gap-2 flex-1">
        <div class="flex items-center justify-between">
            <div class="text-orange-04">
                <p class="uppercase font-semibold text-sm">
                    {{ $product->name }}
                </p>
            </div>
        </div>

        <p class="text-sm">
            {{ $product->getTitleLabelAttribute($lang) }}
        </p>

        <div class="grid grid-cols-[1fr_auto] gap-2 items-center justify-between mt-2 lg:mt-auto">
            <div class="flex items-center flex-wrap gap-1">
                @foreach ($product->variants as $variant)
                    @livewire(
                        'components.variant-item',
                        [
                            'product' => $product,
                            'variant' => $variant,
                        ],
                        key($variant->id)
                    )
                @endforeach
            </div>

            <p class="text-xs">
                @if ($product->variants->count() > 1)
                    {{ $product->variants->count() }} {{ __('colours') }}
                @else
                    {{ __('1 colour') }}
                @endif
            </p>
        </div>
    </div>
</a>
