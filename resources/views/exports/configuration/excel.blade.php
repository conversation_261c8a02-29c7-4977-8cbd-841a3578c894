<table>
    {{-- Heading Info --}}
    <thead>
        <tr>
            <th><strong>{{ __('Code: ') }}</strong></th>
            <th><strong>{{ $config->uuid ?? '' }}</strong></th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <th>{{ __('Name: ') }}</th>
            <th>{{ $config->product->name ?? '' }}</th>
            
        </tr>
        <tr>
            <th>{{ __('Variant: ') }}</th>
            <th>{{ $config->variant->name ?? '' }} - Ref. {{ $module->reference ?? '' }}</th>
        <tr>
        <tr>
            <th>{{ __('Color Code: ') }}</th>
            <th>{{ $config->variant->ral_code ?? '' }}</th>
        <tr>
        <tr>
            <th>{{ __('Assortment: ') }}</th>
            <th>{{ $config->assortment->name ?? '' }}</th>
        <tr>
        </tr>
    </tbody>

    {{-- Preconfigured Drawers --}}
    <thead>
        <tr>
            <th><strong>{{ __('Preconfigured Drawers') }}</strong></th>
        </tr>
    </thead>
    @foreach ($config->drawers->where('locked', true) as $drawer)
        <thead>
        <tr>
            <th>{{ __('Drawer: ') . $drawer->position }}</th>
        </tr>
        <tr>
            <th>{{ __('Name') }}</th>
            <th>{{ __('Title') }}</th>
            <th>{{ __('Reference') }}</th>  
        </tr>
        </thead>
        <tbody>
            @foreach($drawer->modules->sortBy('pivot.position') as $module)
                <tr>
                    <td>{{ $module->name }}</td>
                    <td>{{ $module->getTitleLabelAttribute(app()->getLocale()) }}</td>
                    <td>{{ $module->reference ?? ''}}</td>
                </tr>
            @endforeach
            <tr>
            </tr>
        </tbody>
    @endforeach

    {{-- Custom Drawers --}}
    <thead>
        <tr>
            <th><strong>{{ __('Custom Drawers') }}</strong></th>
        </tr>
    </thead>
    @foreach ($config->drawers->where('locked', false) as $drawer)
        <thead>
        <tr>
            <th>{{ __('Drawer: ') . $drawer->position }}</th>
        </tr>
        <tr>
            <th>{{ __('Name') }}</th>
            <th>{{ __('Title') }}</th>
            <th>{{ __('Reference') }}</th>
        </tr>
        </thead>
        <tbody>
            @foreach($drawer->modules->sortBy('pivot.position') as $module)
                <tr>
                    <td>{{ $module->name }}</td>
                    <td>{{ $module->getTitleLabelAttribute(app()->getLocale()) }}</td>
                    <td>{{ $module->reference ?? ''}}</td>
                </tr>
            @endforeach
            <tr>
            </tr>
        </tbody>
    @endforeach

    {{-- Accessories --}}
    <thead>
        <tr>
            <th><strong>{{ __('Accessories') }}</strong></th>
        </tr>
    </thead>
    @foreach ($config->accessories as $accessory)
        <thead>
        <tr>
            <th>{{ __('Name') }}</th>
            <th>{{ __('Title') }}</th>
            <th>{{ __('Reference') }}</th>
        </tr>
        </thead>
        <tbody>
            <tr>
                <td>{{ $accessory->name }}</td>
                <td>{{ $accessory->getTitleLabelAttribute(app()->getLocale()) }}</td>
                <td>{{ $accessory->reference ?? ''}}</td>
            </tr>
            <tr>
            </tr>
        </tbody>
    @endforeach

</table>