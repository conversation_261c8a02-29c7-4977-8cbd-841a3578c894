@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import the AktivGroteskEx font */
@layer base {
    @font-face {
        font-family: 'AktivGroteskEx';
        font-style: normal;
        font-weight: 900;
        font-display: swap;
        src: url('/public/fonts/AktivGroteskExBlack.ttf') format('truetype');
    }
    @font-face {
        font-family: 'AktivGroteskEx';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url('/public/fonts/AktivGroteskExBold.ttf') format('truetype');
    }
    @font-face {
        font-family: 'AktivGroteskEx';
        font-style: normal;
        font-weight: 300;
        font-display: swap;
        src: url('/public/fonts/AktivGroteskExLight.ttf') format('truetype');
    }
    @font-face {
        font-family: 'AktivGroteskEx';
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url('/public/fonts/AktivGroteskExMedium.ttf') format('truetype');
    }
    @font-face {
        font-family: 'AktivGroteskEx';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url('/public/fonts/AktivGroteskExRegular.ttf') format('truetype');
    }
    @font-face {
        font-family: 'AktivGroteskEx';
        font-style: normal;
        font-weight: 600;
        font-display: swap;
        src: url('/public/fonts/AktivGroteskExSemiBold.ttf') format('truetype');
    }
}