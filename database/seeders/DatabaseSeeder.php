<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Product;
use App\Models\Variant;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\Category;
use App\Models\Accessory;
use App\Models\Assortment;
use App\Models\Module;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Category::factory()->count(5)->create();

        // Product::factory()->count(20)->create();

        // Variant::factory()->count(20)->create();

        // Assortment::factory()->count(20)->create();

        // Accessory::factory()->count(20)->create();

        // Module::factory()->count(30)->create();

        // $this->call([
        //     AssortmentProductSeeder::class,
        //     AccessoryProductSeeder::class,
        //     ModuleProductSeeder::class,
        // ]);
    }
}
