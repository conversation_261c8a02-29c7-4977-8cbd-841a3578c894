<?php

namespace Database\Seeders;

use App\Models\Assortment;
use App\Models\Product;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class AssortmentProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = Product::all();
        $assortments = Assortment::all();

        $products->each(function ($product) use ($assortments) {
            $product->assortments()->attach(
                $assortments->random(rand(1, 5))->pluck('id')->toArray()
            );
        });
    }
}
