<?php

namespace Database\Seeders;

use App\Models\Module;
use App\Models\Product;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class ModuleProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = Product::all();

        $modules = Module::all();

        $products->each(function ($product) use ($modules) {
            $product->modules()->attach(
                $modules->random(rand(1, 5))->mapWithKeys(function ($module) {
                    $orientation = $module->type === 'STD3' ? 'horizontal' : ($module->type === 'STD4' ? 'vertical' : null);
                    return [$module->id => ['orientation' => $orientation]];
                })->toArray()
            );
        });
    }
}
