<?php

namespace Database\Seeders;

use App\Models\Accessory;
use App\Models\Product;
use Illuminate\Database\Seeder;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class AccessoryProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = Product::all();

        $accessories = Accessory::all();

        $products->each(function ($product) use ($accessories) {
            $product->accessories()->attach(
                $accessories->random(rand(1, 5))->pluck('id')->toArray()
            );
        });
    }
}
