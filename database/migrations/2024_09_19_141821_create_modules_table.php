<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('modules', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('name');
            $table->string('reference')->nullable()->comment('Module reference');
            $table->json('title')->nullable()->comment('Module title in multiple languages');
            $table->json('description')->nullable()->comment('Module description in multiple languages');
            $table->string('material')->nullable()->comment('Module material type. Es: rigid, soft.');
            $table->string('image')->nullable()->comment('Relative image URL');
            $table->string('type')->nullable()->comment('Module type. Es: STD4, STD3, MM1.');
            $table->integer('units')->nullable()->comment('Module used units.');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('modules');
    }
};
