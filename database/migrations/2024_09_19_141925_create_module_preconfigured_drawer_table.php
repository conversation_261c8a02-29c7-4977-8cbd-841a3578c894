<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('module_preconfigured_drawer', function (Blueprint $table) {
            $table->id();
            $table->integer('position')->comment('Rappresent the module position in the drawer');
            $table->foreignId('module_id')->constrained()->onDelete('cascade');
            $table->foreignId('preconfigured_drawer_id')->constrained()->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('module_preconfigured_drawer');
    }
};
