<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('variants', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('name');
            $table->string('reference')->nullable()->comment('Variant reference');
            $table->json('title')->nullable()->comment('Variant title in multiple languages');
            $table->json('description')->nullable()->comment('Variant description in multiple languages');
            $table->string('image')->nullable()->comment('Relative image URL');
            $table->string('swatch')->nullable()->comment('Variant swatch icon name');
            $table->string('ral_code')->nullable();
            $table->string('color_hex')->nullable();
            $table->softDeletes();
            $table->timestamps();

            $table->foreignId('product_id')->nullable()->constrained()->onUpdate('cascade')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('variants');
    }
};
