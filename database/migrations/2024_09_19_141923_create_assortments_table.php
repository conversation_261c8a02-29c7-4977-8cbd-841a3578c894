<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('assortments', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('name');
            $table->string('reference')->nullable()->comment('Assortment reference');
            $table->json('title')->nullable()->comment('Title in different languages');
            $table->json('description')->nullable()->comment('Description in different languages');
            $table->string('image')->nullable()->comment('Relative image URL');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('assortments');
    }
};
