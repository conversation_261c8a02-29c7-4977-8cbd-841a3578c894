<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('drawers', function (Blueprint $table) {
            $table->id();
            $table->integer('position')->comment('Rappresent the drawer position in the configuration');
            $table->boolean('locked')->default(false)->comment('Rappresent the drawer lock status, a drawer is locked when an assortment is stored in it');
            $table->integer('units')->nullable()->comment('Rappresent the drawer available units');
            $table->timestamps();

            $table->foreignId('configuration_id')->nullable()->constrained()->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('drawers');
    }
};
