<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('components', function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->string('name');
            $table->string('reference')->nullable()->comment('Component reference');
            $table->json('title')->nullable()->comment('Title in different languages');
            $table->json('description')->nullable()->comment('Description in different languages');
            $table->string('image')->nullable()->comment('Relative image URL');
            $table->integer('pieces')->default(1)->comment('Number of pieces in the component');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('components');
    }
};
