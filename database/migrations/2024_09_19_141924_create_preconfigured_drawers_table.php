<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('preconfigured_drawers', function (Blueprint $table) {
            $table->id();
            $table->integer('position')->comment('Rappresent the drawer position in the configuration');
            $table->string('orientation')->nullable()->comment('Rappresent the module orientation. Es: vertical, horizontal');
            $table->integer('format')->nullable()->comment('Rappresent the max number of single units that can be stored in the drawer. Es: */3, */4, */6');
            $table->timestamps();

            $table->foreignId('assortment_id')->constrained()->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('preconfigured_drawers');
    }
};
