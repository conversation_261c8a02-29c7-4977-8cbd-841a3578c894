<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->boolean('active')->default(true)->comment('Product status');
            $table->string('code')->unique();
            $table->string('name');
            $table->string('reference')->nullable()->comment('Product reference');
            $table->json('title')->nullable()->comment('Product title in multiple languages');
            $table->json('description')->nullable()->comment('Product description in multiple languages');
            $table->string('image')->nullable()->comment('Relative image URL');
            $table->integer('drawers')->comment('Number of drawers');
            $table->string('drawer_template')->nullable()->comment('Front end component template. Es: standard, deep, mm.');
            $table->softDeletes();
            $table->timestamps();

            $table->foreignId('category_id')->nullable()->constrained()->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
