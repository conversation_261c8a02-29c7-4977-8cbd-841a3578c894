<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Assortment>
 */
class AssortmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => $this->faker->unique()->bothify('ASRT-####'),
            'name' => $this->faker->word,
            'title' => [
                'en' => $this->faker->sentence,
                'it' => $this->faker->sentence,
                'es' => $this->faker->sentence,
            ],
            'description' => [
                'en' => $this->faker->sentence,
                'it' => $this->faker->sentence,
                'es' => $this->faker->sentence,
            ],
            'image' => $this->faker->randomElement([
                'chest-1._2631cYo_ZbXvcs.webp',
                'chest-2.DmajGJiB_PoA4N.webp',
                'chest-3.DTIfKN1d_ZDz2dT.webp',
                'chest-4.D809FLIA_ZSJQ91.webp',
                'chest-5.CoHgUYqa_Z1YscR6.webp'
            ]),
        ];
    }
}
