<?php

namespace Database\Factories;

use App\Models\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => $this->faker->unique()->bothify('??###'),
            'name' => $this->faker->name,
            'title' => [
                'en' => $this->faker->sentence,
                'it' => $this->faker->sentence,
                'es' => $this->faker->sentence,
            ],
            'description' => [
                'en' => $this->faker->sentence,
                'it' => $this->faker->sentence,
                'es' => $this->faker->sentence,
            ],
            'image' => $this->faker->randomElement([
                'chest-1._2631cYo_ZbXvcs.webp',
                'chest-2.DmajGJiB_PoA4N.webp',
                'chest-3.DTIfKN1d_ZDz2dT.webp',
                'chest-4.D809FLIA_ZSJQ91.webp',
                'chest-5.CoHgUYqa_Z1YscR6.webp'
            ]),
            'drawers' => $this->faker->numberBetween(4, 10),
            'drawer_template' => $this->faker->randomElement([
                'standard',
                'deep',
                'mm',
            ]),
            'category_id' => Category::inRandomOrder()->first()->id,
        ];
    }
}
