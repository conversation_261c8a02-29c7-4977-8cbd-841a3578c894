<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Module>
 */
class ModuleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => $this->faker->unique()->word(),
            'name' => $this->faker->word(),
            'title' => [
                'en' => $this->faker->sentence(),
                'es' => $this->faker->sentence(),
            ],
            'description' => [
                'en' => $this->faker->paragraph(),
                'es' => $this->faker->paragraph(),
            ],
            'material' => $this->faker->randomElement([
                'rigid',
                'soft',
            ]),
            'type' => $this->faker->randomElement([
                'STD4',
                'STD3',
                // 'MM1',
            ]),
            'image' => function (array $attributes) {
                switch ($attributes['type']) {
                    case 'STD4':
                        return $this->faker->randomElement([
                            'product-card-preview-1.BLV9woTs_Znz6qm.webp',
                        ]);
                    case 'STD3':
                        return $this->faker->randomElement([
                            'product-card-preview-3.IhviOgmq_Z1I4paO.webp',
                        ]);
                    default:
                        return null;
                }
            },
            'units' => function (array $attributes) {
                switch ($attributes['type']) {
                    case 'STD4':
                        return $this->faker->numberBetween(1, 4);
                    case 'STD3':
                        return $this->faker->numberBetween(1, 3);
                    default:
                        return null;
                }
            },
        ];
    }
}
