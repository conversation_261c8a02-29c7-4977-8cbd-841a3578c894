# Beta Utensili - Configurator
This project is a tools box configurator created in Livewire v3 to be completely reactive and with a Laravel Nova administration panel to manage files and datas.

## About the Project
- Core framework is Laravel PHP.
- All the functionalities are developed using Larvel Livewire v3 first party library.<br>
Logic lays in app/livewire and UI in resources/views/livewire.
- Styles are set using Tailwind CSS.
- Database is standard MySql. The project don't relay on other type of structured data o caching systems but MySql.
- Expressive, intuitive [database ORM](https://laravel.com/docs/eloquent).

<PERSON>vel is accessible, powerful, and provides tools required for large, robust applications.
