<?php

use App\Livewire\Pages\Accessory as Accessory;
use App\Livewire\Pages\Home as Home;
use App\Livewire\Pages\Module as Module;
use App\Livewire\Pages\Summary as Summary;
use App\Livewire\Pages\TypeAssortment as TypeAssortment;
use Illuminate\Support\Facades\Route;

/**
 * Route to display the home page.
 *
 * @route GET /
 */
Route::get('/', Home::class)->name('home');

/**
 * Route to display the modules type and assortments page.
 *
 * @route GET /{uuid}/type-assortments
 */
Route::get('/{uuid}/type-assortments', TypeAssortment::class)->name('type-assortments');

/**
 * Route to display the modules page.
 *
 * @route GET /{uuid}/modules
 */
Route::get('/{uuid}/modules', Module::class)->name('modules');

/**
 * Route to display the accessories page.
 *
 * @route GET /{uuid}/accessories
 */
Route::get('/{uuid}/accessories', Accessory::class)->name('accessories');

/**
 * Route to display the summary page.
 *
 * @route GET /{uuid}/summary
 */
Route::get('/{uuid}/summary', Summary::class)->name('summary');
