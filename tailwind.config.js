import defaultTheme from "tailwindcss/defaultTheme";
/** @type {import('tailwindcss').Config} */

export default {
    content: [
        "./resources/**/*.blade.php",
        "./resources/**/*.js",
    ],
    safelist: [
        'col-span-1',
        'col-span-2',
        'col-span-3',
        'col-span-4',
        'col-span-5',
        'col-span-6',
        'row-span-1',
        'row-span-2',
        'row-span-3',
        'row-span-4',
        'row-span-5',
        'row-span-6',
    ],
    theme: {
        extend: {
            keyframes: {
                opacity: {
                    from: { opacity: 0 },
                    to: { opacity: 1 },
                },
            },
            animation: {
                opacity: "opacity 0.3s ease-in-out",
            },
            fontFamily: {
                sans: ["AktivGroteskEx", ...defaultTheme.fontFamily.sans],
            },
            ringWidth: {
                3: "3px",
            },
            boxShadow: {
                sm: "0px 0px 8px 0px",
            },
            gridTemplateColumns: {
                "auto-fill-260": "repeat(auto-fill, minmax(260px, 1fr))",
            },
        },
        colors: {
            transparent: "transparent",
            "neutral-01": "#FAFAFA",
            "neutral-02": "#F1F1F1",
            "neutral-03": "#D9D9D9",
            "neutral-04": "#BFBFBF",
            "neutral-05": "#A6A6A6",
            "neutral-06": "#808080",
            "neutral-07": "#595959",
            "neutral-08": "#404040",
            "neutral-09": "#262626",
            "neutral-10": "#101010",
            "sand-01": "#F7F5F1",
            "sand-02": "#EBE6DC",
            "orange-01": "#FEF7EF",
            "orange-02": "#FDF0E0",
            "orange-03": "#EEAB5F",
            "orange-04": "#EE7F00",
            "orange-05": "#DC6700",
            black: "#000000",
            white: "#FFFFFF",
            green: "#48D07E",
            yellow: "#FFC53D",
        },
        fontSize: {
            xs: "12px",
            sm: "14px",
            base: "16px",
            md: [
                "18px",
                {
                    letterSpacing: "-0.36px",
                },
            ],
            lg: [
                "20px",
                {
                    letterSpacing: "-0.4px",
                },
            ],
            "2lg": ["22px", { letterSpacing: "-0.44px" }],
            xl: [
                "24px",
                {
                    letterSpacing: "-0.48px",
                },
            ],
            "2xl": [
                "28px",
                {
                    letterSpacing: "-0.56px",
                },
            ],
            "3xl": [
                "32px",
                {
                    letterSpacing: "-0.64px",
                },
            ],
            "4xl": [
                "40px",
                {
                    letterSpacing: "-0.8px",
                },
            ],
        },
    },
// eslint-disable-next-line no-undef
plugins: [require("tailwind-scrollbar")({ nocompatible: true })],
};
